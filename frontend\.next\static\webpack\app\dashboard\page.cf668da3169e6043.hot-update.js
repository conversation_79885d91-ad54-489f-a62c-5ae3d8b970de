"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chart-line.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Enhanced mock data for realistic demonstration\nconst agents = [\n    {\n        id: \"agent1\",\n        name: \"智能客服助手\",\n        url: \"https://example.com/agent1\",\n        type: \"客服\",\n        status: \"在线\",\n        lastActive: \"2分钟前\",\n        tasksCompleted: 127,\n        successRate: 98.5\n    },\n    {\n        id: \"agent2\",\n        name: \"数据分析专家\",\n        url: \"https://example.com/agent2\",\n        type: \"分析\",\n        status: \"忙碌\",\n        lastActive: \"刚刚\",\n        tasksCompleted: 89,\n        successRate: 96.2\n    },\n    {\n        id: \"agent3\",\n        name: \"内容创作助理\",\n        url: \"https://example.com/agent3\",\n        type: \"创作\",\n        status: \"在线\",\n        lastActive: \"5分钟前\",\n        tasksCompleted: 156,\n        successRate: 94.8\n    },\n    {\n        id: \"agent4\",\n        name: \"代码审查机器人\",\n        url: \"https://example.com/agent4\",\n        type: \"开发\",\n        status: \"离线\",\n        lastActive: \"1小时前\",\n        tasksCompleted: 73,\n        successRate: 99.1\n    }\n];\nconst dashboardStats = [\n    {\n        title: \"总代理\",\n        value: \"4\",\n        subtitle: \"3个在线\",\n        color: \"bg-blue-500\",\n        trend: \"+2\",\n        trendDirection: \"up\"\n    },\n    {\n        title: \"CPU使用率\",\n        value: \"42.8%\",\n        subtitle: \"平均负载\",\n        color: \"bg-green-500\",\n        trend: \"-5.2%\",\n        trendDirection: \"down\"\n    },\n    {\n        title: \"内存使用\",\n        value: \"67.3%\",\n        subtitle: \"8.2GB / 12GB\",\n        color: \"bg-purple-500\",\n        trend: \"+3.1%\",\n        trendDirection: \"up\"\n    },\n    {\n        title: \"今日任务\",\n        value: \"445\",\n        subtitle: \"已完成\",\n        color: \"bg-orange-500\",\n        trend: \"+28\",\n        trendDirection: \"up\"\n    }\n];\nconst agentActivity = [\n    {\n        id: 1,\n        agent: \"智能客服助手\",\n        action: \"处理用户咨询\",\n        status: \"已完成\",\n        time: \"刚刚\",\n        color: \"bg-green-500\",\n        duration: \"2分钟\"\n    },\n    {\n        id: 2,\n        agent: \"数据分析专家\",\n        action: \"生成销售报告\",\n        status: \"进行中\",\n        time: \"3分钟前\",\n        color: \"bg-blue-500\",\n        duration: \"预计5分钟\"\n    },\n    {\n        id: 3,\n        agent: \"内容创作助理\",\n        action: \"撰写产品描述\",\n        status: \"已完成\",\n        time: \"5分钟前\",\n        color: \"bg-green-500\",\n        duration: \"8分钟\"\n    },\n    {\n        id: 4,\n        agent: \"智能客服助手\",\n        action: \"更新知识库\",\n        status: \"已完成\",\n        time: \"8分钟前\",\n        color: \"bg-green-500\",\n        duration: \"3分钟\"\n    },\n    {\n        id: 5,\n        agent: \"代码审查机器人\",\n        action: \"代码质量检查\",\n        status: \"等待中\",\n        time: \"12分钟前\",\n        color: \"bg-yellow-500\",\n        duration: \"待处理\"\n    }\n];\n// Performance metrics for charts\nconst performanceData = {\n    systemLoad: [\n        {\n            time: \"00:00\",\n            cpu: 35,\n            memory: 62,\n            network: 45\n        },\n        {\n            time: \"04:00\",\n            cpu: 28,\n            memory: 58,\n            network: 38\n        },\n        {\n            time: \"08:00\",\n            cpu: 42,\n            memory: 65,\n            network: 52\n        },\n        {\n            time: \"12:00\",\n            cpu: 48,\n            memory: 71,\n            network: 61\n        },\n        {\n            time: \"16:00\",\n            cpu: 38,\n            memory: 67,\n            network: 47\n        },\n        {\n            time: \"20:00\",\n            cpu: 33,\n            memory: 63,\n            network: 42\n        }\n    ],\n    agentPerformance: [\n        {\n            name: \"智能客服助手\",\n            completed: 127,\n            success: 98.5,\n            avgTime: 3.2\n        },\n        {\n            name: \"数据分析专家\",\n            completed: 89,\n            success: 96.2,\n            avgTime: 12.5\n        },\n        {\n            name: \"内容创作助理\",\n            completed: 156,\n            success: 94.8,\n            avgTime: 8.7\n        },\n        {\n            name: \"代码审查机器人\",\n            completed: 73,\n            success: 99.1,\n            avgTime: 15.3\n        }\n    ],\n    taskDistribution: [\n        {\n            category: \"客服咨询\",\n            count: 185,\n            percentage: 41.6\n        },\n        {\n            category: \"数据分析\",\n            count: 89,\n            percentage: 20.0\n        },\n        {\n            category: \"内容创作\",\n            count: 156,\n            percentage: 35.1\n        },\n        {\n            category: \"代码审查\",\n            count: 15,\n            percentage: 3.3\n        }\n    ]\n};\nfunction DashboardPage() {\n    _s();\n    const [selectedView, setSelectedView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"home\") // Changed from selectedAgent to selectedView for home/agent views\n    ;\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [touchStartX, setTouchStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchCurrentX, setTouchCurrentX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [iframeError, setIframeError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const isLoggedIn = localStorage.getItem(\"isLoggedIn\");\n            const storedUsername = localStorage.getItem(\"username\");\n            if (!isLoggedIn) {\n                router.push(\"/\");\n                return;\n            }\n            if (storedUsername) {\n                setUsername(storedUsername);\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        router\n    ]);\n    // Enhanced mobile menu accessibility and keyboard support\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"DashboardPage.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === 'Escape' && mobileMenuOpen) {\n                        setMobileMenuOpen(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.handleKeyDown\"];\n            const handleResize = {\n                \"DashboardPage.useEffect.handleResize\": ()=>{\n                    // Close mobile menu when switching to desktop view\n                    if (window.innerWidth >= 1024 && mobileMenuOpen) {\n                        setMobileMenuOpen(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.handleResize\"];\n            document.addEventListener('keydown', handleKeyDown);\n            window.addEventListener('resize', handleResize);\n            return ({\n                \"DashboardPage.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleKeyDown);\n                    window.removeEventListener('resize', handleResize);\n                }\n            })[\"DashboardPage.useEffect\"];\n        }\n    }[\"DashboardPage.useEffect\"], [\n        mobileMenuOpen\n    ]);\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"isLoggedIn\");\n        localStorage.removeItem(\"username\");\n        router.push(\"/\");\n    };\n    const toggleSidebar = ()=>{\n        setSidebarCollapsed(!sidebarCollapsed);\n    };\n    const toggleMobileMenu = ()=>{\n        setMobileMenuOpen(!mobileMenuOpen);\n    };\n    // Enhanced touch handling for mobile menu\n    const handleTouchStart = (e)=>{\n        setTouchStartX(e.touches[0].clientX);\n        setTouchCurrentX(e.touches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        if (touchStartX === null) return;\n        setTouchCurrentX(e.touches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (touchStartX === null || touchCurrentX === null) return;\n        const deltaX = touchCurrentX - touchStartX;\n        const threshold = 50 // Minimum swipe distance\n        ;\n        // Swipe right to open menu (when closed)\n        if (deltaX > threshold && !mobileMenuOpen) {\n            setMobileMenuOpen(true);\n        } else if (deltaX < -threshold && mobileMenuOpen) {\n            setMobileMenuOpen(false);\n        }\n        setTouchStartX(null);\n        setTouchCurrentX(null);\n    };\n    // Close mobile menu when clicking outside\n    const handleBackdropClick = (e)=>{\n        e.stopPropagation();\n        setMobileMenuOpen(false);\n    };\n    const handleViewSelect = (view)=>{\n        // Renamed from handleAgentSelect to handleViewSelect\n        if (view === \"home\") {\n            setSelectedView(\"home\");\n            setMobileMenuOpen(false);\n            return;\n        }\n        setIsLoading(true);\n        setIframeError(false);\n        setSelectedView(view);\n        setMobileMenuOpen(false);\n        setTimeout(()=>setIsLoading(false), 1000);\n    };\n    const selectedAgentData = agents.find((agent)=>agent.id === selectedView);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-slate-50 flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n          fixed inset-0 z-40 lg:hidden transition-all duration-300 ease-in-out\\n          \".concat(mobileMenuOpen ? \"bg-black bg-opacity-50 backdrop-blur-sm visible\" : \"bg-transparent invisible\", \"\\n        \"),\n                onClick: handleBackdropClick,\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"\\n          \".concat(mobileMenuOpen ? \"translate-x-0 shadow-2xl\" : \"-translate-x-full shadow-none\", \"\\n          lg:translate-x-0 lg:shadow-none\\n          fixed lg:relative\\n          z-50 lg:z-auto\\n          bg-slate-900\\n          border-r border-slate-700\\n          flex flex-col\\n          transition-all duration-300 ease-out\\n          \").concat(sidebarCollapsed ? \"lg:w-16\" : \"lg:w-80\", \"\\n          w-80\\n          h-screen\\n          lg:transform-none\\n        \"),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-slate-700/50 \".concat(sidebarCollapsed ? 'p-2' : 'p-3'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-3 \".concat(sidebarCollapsed ? 'justify-center' : 'justify-between'),\n                                children: [\n                                    !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Agent 管理系统\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 35\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: toggleSidebar,\n                                                className: \"hidden lg:flex text-slate-400 hover:bg-slate-800/80 hover:text-white rounded-lg transition-all duration-200\",\n                                                children: sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 37\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 76\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: toggleMobileMenu,\n                                                className: \"lg:hidden text-slate-400 hover:bg-slate-800/80 hover:text-white rounded-lg transition-all duration-200 hover:scale-105 active:scale-95\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"transition-transform duration-200 hover:rotate-90\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 11\n                            }, this),\n                            sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-7 h-7 bg-blue-600 rounded-lg flex items-center justify-center cursor-pointer hover:bg-blue-700 transition-all duration-200 hover:scale-105\",\n                                    title: \"\".concat(username || \"手系 Agent\", \" - 管理员\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-3.5 w-3.5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 p-2.5 bg-slate-800/60 rounded-lg mb-3 border border-slate-700/30\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-7 h-7 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-3.5 w-3.5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-white truncate\",\n                                                children: username || \"手系 Agent\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-slate-400\",\n                                                children: \"管理员\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex-1 \".concat(sidebarCollapsed ? 'p-2' : 'p-3', \" overflow-y-auto\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleViewSelect(\"home\"),\n                                        className: \"\\n                  w-full flex items-center rounded-lg text-sm font-medium\\n                  transition-all duration-200 min-h-[36px]\\n                  \".concat(sidebarCollapsed ? 'justify-center px-2 py-2.5' : 'gap-2.5 px-2.5 py-2.5', \"\\n                  \").concat(selectedView === \"home\" ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300 hover:bg-slate-800/60 hover:text-white\", \"\\n                \"),\n                                        title: sidebarCollapsed ? \"首页\" : undefined,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 17\n                                            }, this),\n                                            !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"首页\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 39\n                                            }, this),\n                                            !sidebarCollapsed && selectedView === \"home\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-3 relative group\",\n                                    children: sidebarCollapsed ? /* Collapsed: Show section icon with hover tooltip */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      w-full flex items-center justify-center rounded-lg text-sm font-medium\\n                      transition-all duration-200 min-h-[36px] px-2 py-2.5 cursor-default\\n                      lg:hover:bg-slate-800/60 lg:hover:text-white\\n                      \".concat(agents.some((agent)=>selectedView === agent.id) ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300\", \"\\n                    \"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute left-full top-0 ml-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out z-50 pointer-events-none group-hover:pointer-events-auto transform translate-x-1 group-hover:translate-x-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800/95 backdrop-blur-sm border border-slate-700/60 rounded-lg shadow-2xl min-w-[220px] py-2 before:content-[''] before:absolute before:left-[-6px] before:top-4 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-slate-800/95\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 border-b border-slate-700/50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-xs font-semibold text-slate-300 uppercase tracking-wider flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-blue-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 429,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"AI 专家\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"py-1\",\n                                                            children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleViewSelect(agent.id),\n                                                                    className: \"\\n                              w-full flex items-center gap-2.5 px-3 py-2.5 text-sm font-medium\\n                              transition-all duration-200 hover:bg-slate-700/80 rounded-md mx-1\\n                              \".concat(selectedView === agent.id ? \"bg-blue-600 text-white shadow-md\" : \"text-slate-300 hover:text-white\", \"\\n                            \"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 rounded-full flex-shrink-0 \".concat(agent.status === '在线' ? 'bg-green-500' : agent.status === '忙碌' ? 'bg-yellow-500' : 'bg-gray-400')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 448,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: agent.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 452,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        selectedView === agent.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, agent.id, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /* Expanded: Show section header and all agent items */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-2.5 py-1.5 mb-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-slate-400 uppercase tracking-wider flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"AI 专家\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-0.5\",\n                                                children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleViewSelect(agent.id),\n                                                        className: \"\\n                          w-full flex items-center rounded-lg text-sm font-medium\\n                          transition-all duration-200 min-h-[34px] gap-2.5 px-3 py-2 ml-1\\n                          \".concat(selectedView === agent.id ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300 hover:bg-slate-800/60 hover:text-white\", \"\\n                        \"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full flex-shrink-0 \".concat(agent.status === '在线' ? 'bg-green-500' : agent.status === '忙碌' ? 'bg-yellow-500' : 'bg-gray-400')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"truncate\",\n                                                                children: agent.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            selectedView === agent.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, agent.id, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-3 relative group\",\n                                    children: sidebarCollapsed ? /* Collapsed: Show section icon with hover tooltip */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      w-full flex items-center justify-center rounded-lg text-sm font-medium\\n                      transition-all duration-200 min-h-[36px] px-2 py-2.5 cursor-default\\n                      \".concat(selectedView === \"analytics\" ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300 hover:bg-slate-800/60 hover:text-white\", \"\\n                    \"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute left-full top-0 ml-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out z-50 pointer-events-none group-hover:pointer-events-auto transform translate-x-1 group-hover:translate-x-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800/95 backdrop-blur-sm border border-slate-700/60 rounded-lg shadow-2xl min-w-[220px] py-2 before:content-[''] before:absolute before:left-[-6px] before:top-4 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-slate-800/95\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 border-b border-slate-700/50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-xs font-semibold text-slate-300 uppercase tracking-wider flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-purple-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 527,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"分析工具\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"py-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleViewSelect(\"analytics\"),\n                                                                className: \"\\n                            w-full flex items-center gap-2.5 px-3 py-2.5 text-sm font-medium\\n                            transition-all duration-200 hover:bg-slate-700/80 rounded-md mx-1\\n                            \".concat(selectedView === \"analytics\" ? \"bg-blue-600 text-white shadow-md\" : \"text-slate-300 hover:text-white\", \"\\n                          \"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3.5 w-3.5 flex-shrink-0 text-purple-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 544,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"truncate\",\n                                                                        children: \"性能分析\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 545,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    selectedView === \"analytics\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 547,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /* Expanded: Show section header and analytics items */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-2.5 py-1.5 mb-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-slate-400 uppercase tracking-wider flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"分析工具\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-0.5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleViewSelect(\"analytics\"),\n                                                    className: \"\\n                        w-full flex items-center rounded-lg text-sm font-medium\\n                        transition-all duration-200 min-h-[34px] gap-2.5 px-3 py-2 ml-1\\n                        \".concat(selectedView === \"analytics\" ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300 hover:bg-slate-800/60 hover:text-white\", \"\\n                      \"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-3.5 w-3.5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"truncate\",\n                                                            children: \"性能分析\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        selectedView === \"analytics\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 581,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-slate-700/50 \".concat(sidebarCollapsed ? 'p-2' : 'p-3'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            onClick: handleLogout,\n                            className: \"\\n              w-full text-slate-300 hover:bg-slate-800/60 hover:text-white rounded-lg\\n              transition-all duration-200 min-h-[36px]\\n              \".concat(sidebarCollapsed ? \"justify-center px-2 py-2\" : \"justify-start px-2.5 py-2\", \"\\n            \"),\n                            title: sidebarCollapsed ? \"退出登录\" : undefined,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 13\n                                }, this),\n                                !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm\",\n                                    children: \"退出登录\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 35\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 591,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 310,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: \"ghost\",\n                size: \"sm\",\n                onClick: toggleMobileMenu,\n                className: \"\\n          lg:hidden fixed top-3 left-3 z-30\\n          bg-slate-900/95 backdrop-blur-sm text-white\\n          hover:bg-slate-800/90 active:bg-slate-700\\n          border border-slate-700/40 rounded-lg\\n          transition-all duration-200 ease-in-out\\n          hover:scale-105 active:scale-95\\n          shadow-md hover:shadow-lg\\n          min-h-[36px] min-w-[36px]\\n          \".concat(mobileMenuOpen ? 'bg-slate-800/90 scale-105' : '', \"\\n        \"),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"transition-transform duration-200 \".concat(mobileMenuOpen ? 'rotate-90' : ''),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 628,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 609,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 p-3 lg:p-4 overflow-auto\",\n                children: selectedView === \"home\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-slate-200 p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-slate-900 mb-1\",\n                                    children: [\n                                        \"欢迎回来，\",\n                                        username || \"管理员\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-slate-600\",\n                                    children: \"AI Agent 管理中心概览\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 637,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-3\",\n                            children: dashboardStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-medium text-slate-600\",\n                                                                children: stat.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 649,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-bold text-slate-900 mt-1\",\n                                                                children: stat.value\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 650,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-slate-500\",\n                                                                children: stat.subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 651,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 \".concat(stat.color, \" rounded-lg flex items-center justify-center\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 bg-white rounded opacity-80\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 21\n                                            }, this),\n                                            stat.trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium \".concat(stat.trendDirection === 'up' ? 'text-green-600' : 'text-red-600'),\n                                                        children: [\n                                                            stat.trendDirection === 'up' ? '↗' : '↘',\n                                                            \" \",\n                                                            stat.trend\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-slate-500 ml-1\",\n                                                        children: \"vs 昨天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 658,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 19\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 645,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 643,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Agent 性能\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 676,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: performanceData.agentPerformance.slice(0, 3).map((agent, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-2 bg-slate-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-slate-900 truncate\",\n                                                                    children: agent.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-500\",\n                                                                    children: [\n                                                                        \"成功率: \",\n                                                                        agent.success,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 687,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 685,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-bold text-blue-600\",\n                                                                    children: agent.completed\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-500\",\n                                                                    children: \"任务\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 689,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 675,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 702,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"系统负载\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 701,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 700,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: performanceData.systemLoad.slice(-3).map((load, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-600\",\n                                                                    children: load.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 710,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-900 font-medium\",\n                                                                    children: [\n                                                                        \"CPU: \",\n                                                                        load.cpu,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 711,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 709,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-slate-200 rounded-full h-1.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-purple-600 h-1.5 rounded-full transition-all duration-300\",\n                                                                style: {\n                                                                    width: \"\".concat(load.cpu, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 714,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 708,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 699,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 md:col-span-2 xl:col-span-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"最近活动\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 727,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: agentActivity.slice(0, 3).map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 bg-slate-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full \".concat(activity.color)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 735,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-slate-900 truncate\",\n                                                                    children: activity.agent\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-600 truncate\",\n                                                                    children: activity.action\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 736,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-medium \".concat(activity.status === '已完成' ? 'text-green-600' : activity.status === '进行中' ? 'text-blue-600' : 'text-yellow-600'),\n                                                                children: activity.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 741,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 740,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, activity.id, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 732,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 725,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 673,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"bg-white border-slate-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 text-indigo-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 758,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Agent 状态总览\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 757,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 756,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                        children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-2 bg-slate-50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full \".concat(agent.status === '在线' ? 'bg-green-500' : agent.status === '忙碌' ? 'bg-yellow-500' : 'bg-gray-400')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 767,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs font-medium text-slate-900 truncate\",\n                                                                        children: agent.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 772,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500\",\n                                                                        children: agent.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 773,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 766,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs font-medium text-slate-900\",\n                                                            children: agent.tasksCompleted\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 777,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, agent.id, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 765,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 763,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 762,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 755,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 635,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"h-full bg-white border-slate-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"h-full p-0\",\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-8 w-8 animate-spin text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 791,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-600\",\n                                        children: [\n                                            \"加载 \",\n                                            selectedAgentData === null || selectedAgentData === void 0 ? void 0 : selectedAgentData.name,\n                                            \"...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 792,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 790,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 789,\n                            columnNumber: 17\n                        }, this) : iframeError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-4 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-12 w-12 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 798,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-900 font-medium\",\n                                                children: \"加载失败\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 800,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-600 text-sm mt-1\",\n                                                children: [\n                                                    \"无法加载 \",\n                                                    selectedAgentData === null || selectedAgentData === void 0 ? void 0 : selectedAgentData.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 801,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 799,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>handleViewSelect(selectedView),\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: \"重试\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 803,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 797,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 17\n                        }, this) : selectedAgentData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                            src: selectedAgentData.url,\n                            className: \"w-full h-full border-0 rounded-lg\",\n                            title: selectedAgentData.name,\n                            onError: ()=>setIframeError(true),\n                            sandbox: \"allow-scripts allow-same-origin allow-forms\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 809,\n                            columnNumber: 17\n                        }, this) : null\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 787,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 786,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 633,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 294,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"LZ5QUMNVC8kN/tQl1hjuN5TcKSw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9kYXNoYm9hcmQvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMkM7QUFDQTtBQUNJO0FBQ2dDO0FBZ0IxRDtBQUVyQixpREFBaUQ7QUFDakQsTUFBTXNCLFNBQVM7SUFDYjtRQUNFQyxJQUFJO1FBQ0pDLE1BQU07UUFDTkMsS0FBSztRQUNMQyxNQUFNO1FBQ05DLFFBQVE7UUFDUkMsWUFBWTtRQUNaQyxnQkFBZ0I7UUFDaEJDLGFBQWE7SUFDZjtJQUNBO1FBQ0VQLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxLQUFLO1FBQ0xDLE1BQU07UUFDTkMsUUFBUTtRQUNSQyxZQUFZO1FBQ1pDLGdCQUFnQjtRQUNoQkMsYUFBYTtJQUNmO0lBQ0E7UUFDRVAsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLEtBQUs7UUFDTEMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLFlBQVk7UUFDWkMsZ0JBQWdCO1FBQ2hCQyxhQUFhO0lBQ2Y7SUFDQTtRQUNFUCxJQUFJO1FBQ0pDLE1BQU07UUFDTkMsS0FBSztRQUNMQyxNQUFNO1FBQ05DLFFBQVE7UUFDUkMsWUFBWTtRQUNaQyxnQkFBZ0I7UUFDaEJDLGFBQWE7SUFDZjtDQUNEO0FBRUQsTUFBTUMsaUJBQWlCO0lBQ3JCO1FBQ0VDLE9BQU87UUFDUEMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsT0FBTztRQUNQQyxnQkFBZ0I7SUFDbEI7SUFDQTtRQUNFTCxPQUFPO1FBQ1BDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLE9BQU87UUFDUEMsZ0JBQWdCO0lBQ2xCO0lBQ0E7UUFDRUwsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLGdCQUFnQjtJQUNsQjtJQUNBO1FBQ0VMLE9BQU87UUFDUEMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsT0FBTztRQUNQQyxnQkFBZ0I7SUFDbEI7Q0FDRDtBQUVELE1BQU1DLGdCQUFnQjtJQUNwQjtRQUNFZixJQUFJO1FBQ0pnQixPQUFPO1FBQ1BDLFFBQVE7UUFDUmIsUUFBUTtRQUNSYyxNQUFNO1FBQ05OLE9BQU87UUFDUE8sVUFBVTtJQUNaO0lBQ0E7UUFDRW5CLElBQUk7UUFDSmdCLE9BQU87UUFDUEMsUUFBUTtRQUNSYixRQUFRO1FBQ1JjLE1BQU07UUFDTk4sT0FBTztRQUNQTyxVQUFVO0lBQ1o7SUFDQTtRQUNFbkIsSUFBSTtRQUNKZ0IsT0FBTztRQUNQQyxRQUFRO1FBQ1JiLFFBQVE7UUFDUmMsTUFBTTtRQUNOTixPQUFPO1FBQ1BPLFVBQVU7SUFDWjtJQUNBO1FBQ0VuQixJQUFJO1FBQ0pnQixPQUFPO1FBQ1BDLFFBQVE7UUFDUmIsUUFBUTtRQUNSYyxNQUFNO1FBQ05OLE9BQU87UUFDUE8sVUFBVTtJQUNaO0lBQ0E7UUFDRW5CLElBQUk7UUFDSmdCLE9BQU87UUFDUEMsUUFBUTtRQUNSYixRQUFRO1FBQ1JjLE1BQU07UUFDTk4sT0FBTztRQUNQTyxVQUFVO0lBQ1o7Q0FDRDtBQUVELGlDQUFpQztBQUNqQyxNQUFNQyxrQkFBa0I7SUFDdEJDLFlBQVk7UUFDVjtZQUFFSCxNQUFNO1lBQVNJLEtBQUs7WUFBSUMsUUFBUTtZQUFJQyxTQUFTO1FBQUc7UUFDbEQ7WUFBRU4sTUFBTTtZQUFTSSxLQUFLO1lBQUlDLFFBQVE7WUFBSUMsU0FBUztRQUFHO1FBQ2xEO1lBQUVOLE1BQU07WUFBU0ksS0FBSztZQUFJQyxRQUFRO1lBQUlDLFNBQVM7UUFBRztRQUNsRDtZQUFFTixNQUFNO1lBQVNJLEtBQUs7WUFBSUMsUUFBUTtZQUFJQyxTQUFTO1FBQUc7UUFDbEQ7WUFBRU4sTUFBTTtZQUFTSSxLQUFLO1lBQUlDLFFBQVE7WUFBSUMsU0FBUztRQUFHO1FBQ2xEO1lBQUVOLE1BQU07WUFBU0ksS0FBSztZQUFJQyxRQUFRO1lBQUlDLFNBQVM7UUFBRztLQUNuRDtJQUNEQyxrQkFBa0I7UUFDaEI7WUFBRXhCLE1BQU07WUFBVXlCLFdBQVc7WUFBS0MsU0FBUztZQUFNQyxTQUFTO1FBQUk7UUFDOUQ7WUFBRTNCLE1BQU07WUFBVXlCLFdBQVc7WUFBSUMsU0FBUztZQUFNQyxTQUFTO1FBQUs7UUFDOUQ7WUFBRTNCLE1BQU07WUFBVXlCLFdBQVc7WUFBS0MsU0FBUztZQUFNQyxTQUFTO1FBQUk7UUFDOUQ7WUFBRTNCLE1BQU07WUFBV3lCLFdBQVc7WUFBSUMsU0FBUztZQUFNQyxTQUFTO1FBQUs7S0FDaEU7SUFDREMsa0JBQWtCO1FBQ2hCO1lBQUVDLFVBQVU7WUFBUUMsT0FBTztZQUFLQyxZQUFZO1FBQUs7UUFDakQ7WUFBRUYsVUFBVTtZQUFRQyxPQUFPO1lBQUlDLFlBQVk7UUFBSztRQUNoRDtZQUFFRixVQUFVO1lBQVFDLE9BQU87WUFBS0MsWUFBWTtRQUFLO1FBQ2pEO1lBQUVGLFVBQVU7WUFBUUMsT0FBTztZQUFJQyxZQUFZO1FBQUk7S0FDaEQ7QUFDSDtBQUVlLFNBQVNDOztJQUN0QixNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHekQsK0NBQVFBLENBQVMsUUFBUSxrRUFBa0U7O0lBQ25JLE1BQU0sQ0FBQzBELFVBQVVDLFlBQVksR0FBRzNELCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQzRELGtCQUFrQkMsb0JBQW9CLEdBQUc3RCwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUM4RCxnQkFBZ0JDLGtCQUFrQixHQUFHL0QsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDZ0UsYUFBYUMsZUFBZSxHQUFHakUsK0NBQVFBLENBQWdCO0lBQzlELE1BQU0sQ0FBQ2tFLGVBQWVDLGlCQUFpQixHQUFHbkUsK0NBQVFBLENBQWdCO0lBQ2xFLE1BQU0sQ0FBQ29FLFdBQVdDLGFBQWEsR0FBR3JFLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ3NFLGFBQWFDLGVBQWUsR0FBR3ZFLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU13RSxTQUFTdkUsMERBQVNBO0lBRXhCRixnREFBU0E7bUNBQUM7WUFDUixNQUFNMEUsYUFBYUMsYUFBYUMsT0FBTyxDQUFDO1lBQ3hDLE1BQU1DLGlCQUFpQkYsYUFBYUMsT0FBTyxDQUFDO1lBRTVDLElBQUksQ0FBQ0YsWUFBWTtnQkFDZkQsT0FBT0ssSUFBSSxDQUFDO2dCQUNaO1lBQ0Y7WUFFQSxJQUFJRCxnQkFBZ0I7Z0JBQ2xCakIsWUFBWWlCO1lBQ2Q7UUFDRjtrQ0FBRztRQUFDSjtLQUFPO0lBRVgsMERBQTBEO0lBQzFEekUsZ0RBQVNBO21DQUFDO1lBQ1IsTUFBTStFO3lEQUFnQixDQUFDQztvQkFDckIsSUFBSUEsRUFBRUMsR0FBRyxLQUFLLFlBQVlsQixnQkFBZ0I7d0JBQ3hDQyxrQkFBa0I7b0JBQ3BCO2dCQUNGOztZQUVBLE1BQU1rQjt3REFBZTtvQkFDbkIsbURBQW1EO29CQUNuRCxJQUFJQyxPQUFPQyxVQUFVLElBQUksUUFBUXJCLGdCQUFnQjt3QkFDL0NDLGtCQUFrQjtvQkFDcEI7Z0JBQ0Y7O1lBRUFxQixTQUFTQyxnQkFBZ0IsQ0FBQyxXQUFXUDtZQUNyQ0ksT0FBT0csZ0JBQWdCLENBQUMsVUFBVUo7WUFFbEM7MkNBQU87b0JBQ0xHLFNBQVNFLG1CQUFtQixDQUFDLFdBQVdSO29CQUN4Q0ksT0FBT0ksbUJBQW1CLENBQUMsVUFBVUw7Z0JBQ3ZDOztRQUNGO2tDQUFHO1FBQUNuQjtLQUFlO0lBRW5CLE1BQU15QixlQUFlO1FBQ25CYixhQUFhYyxVQUFVLENBQUM7UUFDeEJkLGFBQWFjLFVBQVUsQ0FBQztRQUN4QmhCLE9BQU9LLElBQUksQ0FBQztJQUNkO0lBRUEsTUFBTVksZ0JBQWdCO1FBQ3BCNUIsb0JBQW9CLENBQUNEO0lBQ3ZCO0lBRUEsTUFBTThCLG1CQUFtQjtRQUN2QjNCLGtCQUFrQixDQUFDRDtJQUNyQjtJQUVBLDBDQUEwQztJQUMxQyxNQUFNNkIsbUJBQW1CLENBQUNaO1FBQ3hCZCxlQUFlYyxFQUFFYSxPQUFPLENBQUMsRUFBRSxDQUFDQyxPQUFPO1FBQ25DMUIsaUJBQWlCWSxFQUFFYSxPQUFPLENBQUMsRUFBRSxDQUFDQyxPQUFPO0lBQ3ZDO0lBRUEsTUFBTUMsa0JBQWtCLENBQUNmO1FBQ3ZCLElBQUlmLGdCQUFnQixNQUFNO1FBQzFCRyxpQkFBaUJZLEVBQUVhLE9BQU8sQ0FBQyxFQUFFLENBQUNDLE9BQU87SUFDdkM7SUFFQSxNQUFNRSxpQkFBaUI7UUFDckIsSUFBSS9CLGdCQUFnQixRQUFRRSxrQkFBa0IsTUFBTTtRQUVwRCxNQUFNOEIsU0FBUzlCLGdCQUFnQkY7UUFDL0IsTUFBTWlDLFlBQVksR0FBRyx5QkFBeUI7O1FBRTlDLHlDQUF5QztRQUN6QyxJQUFJRCxTQUFTQyxhQUFhLENBQUNuQyxnQkFBZ0I7WUFDekNDLGtCQUFrQjtRQUNwQixPQUVLLElBQUlpQyxTQUFTLENBQUNDLGFBQWFuQyxnQkFBZ0I7WUFDOUNDLGtCQUFrQjtRQUNwQjtRQUVBRSxlQUFlO1FBQ2ZFLGlCQUFpQjtJQUNuQjtJQUVBLDBDQUEwQztJQUMxQyxNQUFNK0Isc0JBQXNCLENBQUNuQjtRQUMzQkEsRUFBRW9CLGVBQWU7UUFDakJwQyxrQkFBa0I7SUFDcEI7SUFFQSxNQUFNcUMsbUJBQW1CLENBQUNDO1FBQ3hCLHFEQUFxRDtRQUNyRCxJQUFJQSxTQUFTLFFBQVE7WUFDbkI1QyxnQkFBZ0I7WUFDaEJNLGtCQUFrQjtZQUNsQjtRQUNGO1FBRUFNLGFBQWE7UUFDYkUsZUFBZTtRQUNmZCxnQkFBZ0I0QztRQUNoQnRDLGtCQUFrQjtRQUNsQnVDLFdBQVcsSUFBTWpDLGFBQWEsUUFBUTtJQUN4QztJQUlBLE1BQU1rQyxvQkFBb0JsRixPQUFPbUYsSUFBSSxDQUFDLENBQUNsRSxRQUFVQSxNQUFNaEIsRUFBRSxLQUFLa0M7SUFFOUQscUJBQ0UsOERBQUNpRDtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQ0NDLFdBQVcsK0ZBS1IsT0FIQzVDLGlCQUNFLG9EQUNBLDRCQUNIO2dCQUVINkMsU0FBU1Q7Z0JBQ1RVLGNBQWNqQjtnQkFDZGtCLGFBQWFmO2dCQUNiZ0IsWUFBWWY7Ozs7OzswQkFHZCw4REFBQ2dCO2dCQUNDTCxXQUFXLGVBU1A5QyxPQVJBRSxpQkFBaUIsNkJBQTZCLGlDQUFnQyx3UEFRckMsT0FBekNGLG1CQUFtQixZQUFZLFdBQVU7Z0JBSzdDZ0QsY0FBY2pCO2dCQUNka0IsYUFBYWY7Z0JBQ2JnQixZQUFZZjs7a0NBRVosOERBQUNVO3dCQUFJQyxXQUFXLGdDQUFpRSxPQUFqQzlDLG1CQUFtQixRQUFROzswQ0FDekUsOERBQUM2QztnQ0FBSUMsV0FBVywwQkFBa0YsT0FBeEQ5QyxtQkFBbUIsbUJBQW1COztvQ0FDN0UsQ0FBQ0Esa0NBQW9CLDhEQUFDb0Q7d0NBQUdOLFdBQVU7a0RBQW1DOzs7Ozs7a0RBQ3ZFLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUN4Ryx5REFBTUE7Z0RBQ0wrRyxTQUFRO2dEQUNSQyxNQUFLO2dEQUNMUCxTQUFTbEI7Z0RBQ1RpQixXQUFVOzBEQUVUOUMsaUNBQW1CLDhEQUFDL0MseUxBQVlBO29EQUFDNkYsV0FBVTs7Ozs7eUVBQWUsOERBQUM5Rix5TEFBV0E7b0RBQUM4RixXQUFVOzs7Ozs7Ozs7OzswREFFcEYsOERBQUN4Ryx5REFBTUE7Z0RBQ0wrRyxTQUFRO2dEQUNSQyxNQUFLO2dEQUNMUCxTQUFTakI7Z0RBQ1RnQixXQUFVOzBEQUVWLDRFQUFDRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ2hHLHlMQUFDQTt3REFBQ2dHLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBT3BCOUMsaUNBQ0MsOERBQUM2QztnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQ0NDLFdBQVU7b0NBQ1YzRSxPQUFPLEdBQTBCLE9BQXZCMkIsWUFBWSxZQUFXOzhDQUVqQyw0RUFBQ2xELHlMQUFJQTt3Q0FBQ2tHLFdBQVU7Ozs7Ozs7Ozs7Ozs7OztxREFJcEIsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNsRyx5TEFBSUE7NENBQUNrRyxXQUFVOzs7Ozs7Ozs7OztrREFFbEIsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ1M7Z0RBQUVULFdBQVU7MERBQTJDaEQsWUFBWTs7Ozs7OzBEQUNwRSw4REFBQ3lEO2dEQUFFVCxXQUFVOzBEQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU05Qyw4REFBQ1U7d0JBQUlWLFdBQVcsVUFBMkMsT0FBakM5QyxtQkFBbUIsUUFBUSxPQUFNO2tDQUV6RCw0RUFBQzZDOzRCQUFJQyxXQUFVOzs4Q0FFYiw4REFBQ0Q7OENBQ0MsNEVBQUNZO3dDQUNDVixTQUFTLElBQU1QLGlCQUFpQjt3Q0FDaENNLFdBQVcsOEpBS1BsRCxPQUZBSSxtQkFBbUIsK0JBQStCLHlCQUF3Qix3QkFLM0UsT0FIQ0osaUJBQWlCLFNBQ2IsK0RBQ0EseURBQ0w7d0NBRUh6QixPQUFPNkIsbUJBQW1CLE9BQU8wRDs7MERBRWpDLDhEQUFDckcseUxBQUlBO2dEQUFDeUYsV0FBVTs7Ozs7OzRDQUNmLENBQUM5QyxrQ0FBb0IsOERBQUMyRDswREFBSzs7Ozs7OzRDQUMzQixDQUFDM0Qsb0JBQW9CSixpQkFBaUIsd0JBQ3JDLDhEQUFDaUQ7Z0RBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU1yQiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1o5QyxtQkFDQyxtREFBbUQsaUJBQ25EOzswREFDRSw4REFBQzZDO2dEQUNDQyxXQUFXLHdSQVFSLE9BSENyRixPQUFPbUcsSUFBSSxDQUFDbEYsQ0FBQUEsUUFBU2tCLGlCQUFpQmxCLE1BQU1oQixFQUFFLElBQzFDLCtEQUNBLGtCQUNMOzBEQUdILDRFQUFDSiwwTEFBS0E7b0RBQUN3RixXQUFVOzs7Ozs7Ozs7OzswREFJbkIsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVO3NFQUNiLDRFQUFDZTtnRUFBR2YsV0FBVTs7a0ZBQ1osOERBQUN4RiwwTEFBS0E7d0VBQUN3RixXQUFVOzs7Ozs7b0VBQTBCOzs7Ozs7Ozs7Ozs7c0VBSS9DLDhEQUFDRDs0REFBSUMsV0FBVTtzRUFDWnJGLE9BQU9xRyxHQUFHLENBQUMsQ0FBQ3BGLHNCQUNYLDhEQUFDK0U7b0VBRUNWLFNBQVMsSUFBTVAsaUJBQWlCOUQsTUFBTWhCLEVBQUU7b0VBQ3hDb0YsV0FBVyxvT0FPUixPQUhDbEQsaUJBQWlCbEIsTUFBTWhCLEVBQUUsR0FDckIscUNBQ0EsbUNBQ0w7O3NGQUdILDhEQUFDbUY7NEVBQUlDLFdBQVcsc0NBR2YsT0FGQ3BFLE1BQU1aLE1BQU0sS0FBSyxPQUFPLGlCQUN4QlksTUFBTVosTUFBTSxLQUFLLE9BQU8sa0JBQWtCOzs7Ozs7c0ZBRTVDLDhEQUFDNkY7NEVBQUtiLFdBQVU7c0ZBQVlwRSxNQUFNZixJQUFJOzs7Ozs7d0VBQ3JDaUMsaUJBQWlCbEIsTUFBTWhCLEVBQUUsa0JBQ3hCLDhEQUFDbUY7NEVBQUlDLFdBQVU7Ozs7Ozs7bUVBbEJacEUsTUFBTWhCLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dURBMkJ6QixxREFBcUQsaUJBQ3JEOzswREFDRSw4REFBQ21GO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDaUI7b0RBQUdqQixXQUFVOztzRUFDWiw4REFBQ3hGLDBMQUFLQTs0REFBQ3dGLFdBQVU7Ozs7Ozt3REFBWTs7Ozs7Ozs7Ozs7OzBEQU1qQyw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ1pyRixPQUFPcUcsR0FBRyxDQUFDLENBQUNwRixzQkFDWCw4REFBQytFO3dEQUVDVixTQUFTLElBQU1QLGlCQUFpQjlELE1BQU1oQixFQUFFO3dEQUN4Q29GLFdBQVcsNk1BT1IsT0FIQ2xELGlCQUFpQmxCLE1BQU1oQixFQUFFLEdBQ3JCLCtEQUNBLHlEQUNMOzswRUFHSCw4REFBQ21GO2dFQUFJQyxXQUFXLHNDQUdmLE9BRkNwRSxNQUFNWixNQUFNLEtBQUssT0FBTyxpQkFDeEJZLE1BQU1aLE1BQU0sS0FBSyxPQUFPLGtCQUFrQjs7Ozs7OzBFQUU1Qyw4REFBQzZGO2dFQUFLYixXQUFVOzBFQUFZcEUsTUFBTWYsSUFBSTs7Ozs7OzREQUNyQ2lDLGlCQUFpQmxCLE1BQU1oQixFQUFFLGtCQUN4Qiw4REFBQ21GO2dFQUFJQyxXQUFVOzs7Ozs7O3VEQWxCWnBFLE1BQU1oQixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0E0QnpCLDhEQUFDbUY7b0NBQUlDLFdBQVU7OENBQ1o5QyxtQkFDQyxtREFBbUQsaUJBQ25EOzswREFDRSw4REFBQzZDO2dEQUNDQyxXQUFXLG9OQU9SLE9BSENsRCxpQkFBaUIsY0FDYiwrREFDQSx5REFDTDswREFHSCw0RUFBQ3JDLDBMQUFTQTtvREFBQ3VGLFdBQVU7Ozs7Ozs7Ozs7OzBEQUl2Qiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7c0VBQ2IsNEVBQUNlO2dFQUFHZixXQUFVOztrRkFDWiw4REFBQ3ZGLDBMQUFTQTt3RUFBQ3VGLFdBQVU7Ozs7OztvRUFBNEI7Ozs7Ozs7Ozs7OztzRUFJckQsOERBQUNEOzREQUFJQyxXQUFVO3NFQUNiLDRFQUFDVztnRUFDQ1YsU0FBUyxJQUFNUCxpQkFBaUI7Z0VBQ2hDTSxXQUFXLDhOQU9SLE9BSENsRCxpQkFBaUIsY0FDYixxQ0FDQSxtQ0FDTDs7a0ZBR0gsOERBQUNwQywwTEFBU0E7d0VBQUNzRixXQUFVOzs7Ozs7a0ZBQ3JCLDhEQUFDYTt3RUFBS2IsV0FBVTtrRkFBVzs7Ozs7O29FQUMxQmxELGlCQUFpQiw2QkFDaEIsOERBQUNpRDt3RUFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dURBUTNCLHFEQUFxRCxpQkFDckQ7OzBEQUNFLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ2lCO29EQUFHakIsV0FBVTs7c0VBQ1osOERBQUN2RiwwTEFBU0E7NERBQUN1RixXQUFVOzs7Ozs7d0RBQVk7Ozs7Ozs7Ozs7OzswREFNckMsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDVztvREFDQ1YsU0FBUyxJQUFNUCxpQkFBaUI7b0RBQ2hDTSxXQUFXLHVNQU9SLE9BSENsRCxpQkFBaUIsY0FDYiwrREFDQSx5REFDTDs7c0VBR0gsOERBQUNwQywwTEFBU0E7NERBQUNzRixXQUFVOzs7Ozs7c0VBQ3JCLDhEQUFDYTs0REFBS2IsV0FBVTtzRUFBVzs7Ozs7O3dEQUMxQmxELGlCQUFpQiw2QkFDaEIsOERBQUNpRDs0REFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBVS9CLDhEQUFDRDt3QkFBSUMsV0FBVyxnQ0FBaUUsT0FBakM5QyxtQkFBbUIsUUFBUTtrQ0FDekUsNEVBQUMxRCx5REFBTUE7NEJBQ0wrRyxTQUFROzRCQUNSTixTQUFTcEI7NEJBQ1RtQixXQUFXLGtLQUdxRSxPQUE1RTlDLG1CQUFtQiw2QkFBNkIsNkJBQTRCOzRCQUVoRjdCLE9BQU82QixtQkFBbUIsU0FBUzBEOzs4Q0FFbkMsOERBQUMvRywwTEFBTUE7b0NBQUNtRyxXQUFVOzs7Ozs7Z0NBQ2pCLENBQUM5QyxrQ0FBb0IsOERBQUMyRDtvQ0FBS2IsV0FBVTs4Q0FBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTTNELDhEQUFDeEcseURBQU1BO2dCQUNMK0csU0FBUTtnQkFDUkMsTUFBSztnQkFDTFAsU0FBU2pCO2dCQUNUZ0IsV0FBVyxpWUFTMkMsT0FBbEQ1QyxpQkFBaUIsOEJBQThCLElBQUc7Z0JBRXREOEMsY0FBY2pCO2dCQUNka0IsYUFBYWY7Z0JBQ2JnQixZQUFZZjswQkFFWiw0RUFBQ1U7b0JBQUlDLFdBQVcscUNBQXVFLE9BQWxDNUMsaUJBQWlCLGNBQWM7OEJBQ2xGLDRFQUFDckQsMExBQUlBO3dCQUFDaUcsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OzswQkFJcEIsOERBQUNrQjtnQkFBS2xCLFdBQVU7MEJBQ2JsRCxpQkFBaUIsdUJBQ2hCLDhEQUFDaUQ7b0JBQUlDLFdBQVU7O3NDQUViLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNNO29DQUFHTixXQUFVOzt3Q0FBd0M7d0NBQU1oRCxZQUFZOzs7Ozs7OzhDQUN4RSw4REFBQ3lEO29DQUFFVCxXQUFVOzhDQUF5Qjs7Ozs7Ozs7Ozs7O3NDQUl4Qyw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ1o1RSxlQUFlNEYsR0FBRyxDQUFDLENBQUNHLE1BQU1DLHNCQUN6Qiw4REFBQzNILHFEQUFJQTtvQ0FBYXVHLFdBQVU7OENBQzFCLDRFQUFDdEcsNERBQVdBO3dDQUFDc0csV0FBVTs7MERBQ3JCLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEOzswRUFDQyw4REFBQ1U7Z0VBQUVULFdBQVU7MEVBQXNDbUIsS0FBSzlGLEtBQUs7Ozs7OzswRUFDN0QsOERBQUNvRjtnRUFBRVQsV0FBVTswRUFBeUNtQixLQUFLN0YsS0FBSzs7Ozs7OzBFQUNoRSw4REFBQ21GO2dFQUFFVCxXQUFVOzBFQUEwQm1CLEtBQUs1RixRQUFROzs7Ozs7Ozs7Ozs7a0VBRXRELDhEQUFDd0U7d0RBQUlDLFdBQVcsYUFBd0IsT0FBWG1CLEtBQUszRixLQUFLLEVBQUM7a0VBQ3RDLDRFQUFDdUU7NERBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzRDQUdsQm1CLEtBQUsxRixLQUFLLGtCQUNULDhEQUFDc0U7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDYTt3REFBS2IsV0FBVyx1QkFFaEIsT0FEQ21CLEtBQUt6RixjQUFjLEtBQUssT0FBTyxtQkFBbUI7OzREQUVqRHlGLEtBQUt6RixjQUFjLEtBQUssT0FBTyxNQUFNOzREQUFJOzREQUFFeUYsS0FBSzFGLEtBQUs7Ozs7Ozs7a0VBRXhELDhEQUFDb0Y7d0RBQUtiLFdBQVU7a0VBQThCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7bUNBbkIzQ29COzs7Ozs7Ozs7O3NDQTRCZiw4REFBQ3JCOzRCQUFJQyxXQUFVOzs4Q0FFYiw4REFBQ3ZHLHFEQUFJQTtvQ0FBQ3VHLFdBQVU7O3NEQUNkLDhEQUFDckcsMkRBQVVBOzRDQUFDcUcsV0FBVTtzREFDcEIsNEVBQUNwRywwREFBU0E7Z0RBQUNvRyxXQUFVOztrRUFDbkIsOERBQUN2RiwwTEFBU0E7d0RBQUN1RixXQUFVOzs7Ozs7b0RBQTBCOzs7Ozs7Ozs7Ozs7c0RBSW5ELDhEQUFDdEcsNERBQVdBOzRDQUFDc0csV0FBVTtzREFDcEJoRSxnQkFBZ0JLLGdCQUFnQixDQUFDZ0YsS0FBSyxDQUFDLEdBQUcsR0FBR0wsR0FBRyxDQUFDLENBQUNwRixPQUFPd0Ysc0JBQ3hELDhEQUFDckI7b0RBQWdCQyxXQUFVOztzRUFDekIsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ1M7b0VBQUVULFdBQVU7OEVBQStDcEUsTUFBTWYsSUFBSTs7Ozs7OzhFQUN0RSw4REFBQzRGO29FQUFFVCxXQUFVOzt3RUFBeUI7d0VBQU1wRSxNQUFNVyxPQUFPO3dFQUFDOzs7Ozs7Ozs7Ozs7O3NFQUU1RCw4REFBQ3dEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ1M7b0VBQUVULFdBQVU7OEVBQW1DcEUsTUFBTVUsU0FBUzs7Ozs7OzhFQUMvRCw4REFBQ21FO29FQUFFVCxXQUFVOzhFQUF5Qjs7Ozs7Ozs7Ozs7OzttREFQaENvQjs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FlaEIsOERBQUMzSCxxREFBSUE7b0NBQUN1RyxXQUFVOztzREFDZCw4REFBQ3JHLDJEQUFVQTs0Q0FBQ3FHLFdBQVU7c0RBQ3BCLDRFQUFDcEcsMERBQVNBO2dEQUFDb0csV0FBVTs7a0VBQ25CLDhEQUFDdEYsMExBQVNBO3dEQUFDc0YsV0FBVTs7Ozs7O29EQUE0Qjs7Ozs7Ozs7Ozs7O3NEQUlyRCw4REFBQ3RHLDREQUFXQTs0Q0FBQ3NHLFdBQVU7c0RBQ3BCaEUsZ0JBQWdCQyxVQUFVLENBQUNvRixLQUFLLENBQUMsQ0FBQyxHQUFHTCxHQUFHLENBQUMsQ0FBQ00sTUFBTUYsc0JBQy9DLDhEQUFDckI7b0RBQWdCQyxXQUFVOztzRUFDekIsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ2E7b0VBQUtiLFdBQVU7OEVBQWtCc0IsS0FBS3hGLElBQUk7Ozs7Ozs4RUFDM0MsOERBQUMrRTtvRUFBS2IsV0FBVTs7d0VBQTZCO3dFQUFNc0IsS0FBS3BGLEdBQUc7d0VBQUM7Ozs7Ozs7Ozs7Ozs7c0VBRTlELDhEQUFDNkQ7NERBQUlDLFdBQVU7c0VBQ2IsNEVBQUNEO2dFQUNDQyxXQUFVO2dFQUNWdUIsT0FBTztvRUFBRUMsT0FBTyxHQUFZLE9BQVRGLEtBQUtwRixHQUFHLEVBQUM7Z0VBQUc7Ozs7Ozs7Ozs7OzttREFSM0JrRjs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FpQmhCLDhEQUFDM0gscURBQUlBO29DQUFDdUcsV0FBVTs7c0RBQ2QsOERBQUNyRywyREFBVUE7NENBQUNxRyxXQUFVO3NEQUNwQiw0RUFBQ3BHLDBEQUFTQTtnREFBQ29HLFdBQVU7O2tFQUNuQiw4REFBQy9GLDBMQUFRQTt3REFBQytGLFdBQVU7Ozs7OztvREFBNEI7Ozs7Ozs7Ozs7OztzREFJcEQsOERBQUN0Ryw0REFBV0E7NENBQUNzRyxXQUFVO3NEQUNwQnJFLGNBQWMwRixLQUFLLENBQUMsR0FBRyxHQUFHTCxHQUFHLENBQUMsQ0FBQ1MseUJBQzlCLDhEQUFDMUI7b0RBQXNCQyxXQUFVOztzRUFDL0IsOERBQUNEOzREQUFJQyxXQUFXLHdCQUF1QyxPQUFmeUIsU0FBU2pHLEtBQUs7Ozs7OztzRUFDdEQsOERBQUN1RTs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNTO29FQUFFVCxXQUFVOzhFQUErQ3lCLFNBQVM3RixLQUFLOzs7Ozs7OEVBQzFFLDhEQUFDNkU7b0VBQUVULFdBQVU7OEVBQW1DeUIsU0FBUzVGLE1BQU07Ozs7Ozs7Ozs7OztzRUFFakUsOERBQUNrRTs0REFBSUMsV0FBVTtzRUFDYiw0RUFBQ1M7Z0VBQUVULFdBQVcsdUJBR2IsT0FGQ3lCLFNBQVN6RyxNQUFNLEtBQUssUUFBUSxtQkFDNUJ5RyxTQUFTekcsTUFBTSxLQUFLLFFBQVEsa0JBQWtCOzBFQUU3Q3lHLFNBQVN6RyxNQUFNOzs7Ozs7Ozs7Ozs7bURBWFp5RyxTQUFTN0csRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FxQjdCLDhEQUFDbkIscURBQUlBOzRCQUFDdUcsV0FBVTs7OENBQ2QsOERBQUNyRywyREFBVUE7b0NBQUNxRyxXQUFVOzhDQUNwQiw0RUFBQ3BHLDBEQUFTQTt3Q0FBQ29HLFdBQVU7OzBEQUNuQiw4REFBQzVGLDBMQUFLQTtnREFBQzRGLFdBQVU7Ozs7Ozs0Q0FBNEI7Ozs7Ozs7Ozs7Ozs4Q0FJakQsOERBQUN0Ryw0REFBV0E7OENBQ1YsNEVBQUNxRzt3Q0FBSUMsV0FBVTtrREFDWnJGLE9BQU9xRyxHQUFHLENBQUMsQ0FBQ3BGLHNCQUNYLDhEQUFDbUU7Z0RBQW1CQyxXQUFVOztrRUFDNUIsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVcsd0JBR2YsT0FGQ3BFLE1BQU1aLE1BQU0sS0FBSyxPQUFPLGlCQUN4QlksTUFBTVosTUFBTSxLQUFLLE9BQU8sa0JBQWtCOzs7Ozs7MEVBRTVDLDhEQUFDK0U7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDUzt3RUFBRVQsV0FBVTtrRkFBK0NwRSxNQUFNZixJQUFJOzs7Ozs7a0ZBQ3RFLDhEQUFDNEY7d0VBQUVULFdBQVU7a0ZBQTBCcEUsTUFBTWIsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQUdyRCw4REFBQ2dGO3dEQUFJQyxXQUFVO2tFQUNiLDRFQUFDUzs0REFBRVQsV0FBVTtzRUFBc0NwRSxNQUFNVixjQUFjOzs7Ozs7Ozs7Ozs7K0NBWmpFVSxNQUFNaEIsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7eUNBcUI1Qiw4REFBQ25CLHFEQUFJQTtvQkFBQ3VHLFdBQVU7OEJBQ2QsNEVBQUN0Ryw0REFBV0E7d0JBQUNzRyxXQUFVO2tDQUNwQnRDLDBCQUNDLDhEQUFDcUM7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQzNGLDBMQUFPQTt3Q0FBQzJGLFdBQVU7Ozs7OztrREFDbkIsOERBQUNTO3dDQUFFVCxXQUFVOzs0Q0FBaUI7NENBQUlILDhCQUFBQSx3Q0FBQUEsa0JBQW1CaEYsSUFBSTs0Q0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7bUNBRzVEK0MsNEJBQ0YsOERBQUNtQzs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDMUYsMExBQVdBO3dDQUFDMEYsV0FBVTs7Ozs7O2tEQUN2Qiw4REFBQ0Q7OzBEQUNDLDhEQUFDVTtnREFBRVQsV0FBVTswREFBNkI7Ozs7OzswREFDMUMsOERBQUNTO2dEQUFFVCxXQUFVOztvREFBOEI7b0RBQU1ILDhCQUFBQSx3Q0FBQUEsa0JBQW1CaEYsSUFBSTs7Ozs7Ozs7Ozs7OztrREFFMUUsOERBQUNyQix5REFBTUE7d0NBQUN5RyxTQUFTLElBQU1QLGlCQUFpQjVDO3dDQUFleUQsU0FBUTt3Q0FBVUMsTUFBSztrREFBSzs7Ozs7Ozs7Ozs7Ozs7OzttQ0FLckZYLGtDQUNGLDhEQUFDNkI7NEJBQ0NDLEtBQUs5QixrQkFBa0IvRSxHQUFHOzRCQUMxQmtGLFdBQVU7NEJBQ1YzRSxPQUFPd0Usa0JBQWtCaEYsSUFBSTs0QkFDN0IrRyxTQUFTLElBQU0vRCxlQUFlOzRCQUM5QmdFLFNBQVE7Ozs7O21DQUVSOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT2xCO0dBeG9Cd0JoRjs7UUFTUHRELHNEQUFTQTs7O0tBVEZzRCIsInNvdXJjZXMiOlsiRDpcXG15X3Byb2plY3RcXHdvcmtcXGhlcm1lc1xcZnJvbnRlbmRcXGFwcFxcZGFzaGJvYXJkXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIlxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIlxuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiXG5pbXBvcnQge1xuICBMb2dPdXQsXG4gIFVzZXIsXG4gIE1lbnUsXG4gIFgsXG4gIEFjdGl2aXR5LFxuICBDaGV2cm9uTGVmdCxcbiAgQ2hldnJvblJpZ2h0LFxuICBVc2VycyxcbiAgTG9hZGVyMixcbiAgQWxlcnRDaXJjbGUsXG4gIEhvbWUsXG4gIEJyYWluLFxuICBCYXJDaGFydDMsXG4gIExpbmVDaGFydCxcbn0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiXG5cbi8vIEVuaGFuY2VkIG1vY2sgZGF0YSBmb3IgcmVhbGlzdGljIGRlbW9uc3RyYXRpb25cbmNvbnN0IGFnZW50cyA9IFtcbiAge1xuICAgIGlkOiBcImFnZW50MVwiLFxuICAgIG5hbWU6IFwi5pm66IO95a6i5pyN5Yqp5omLXCIsXG4gICAgdXJsOiBcImh0dHBzOi8vZXhhbXBsZS5jb20vYWdlbnQxXCIsXG4gICAgdHlwZTogXCLlrqLmnI1cIixcbiAgICBzdGF0dXM6IFwi5Zyo57q/XCIsXG4gICAgbGFzdEFjdGl2ZTogXCIy5YiG6ZKf5YmNXCIsXG4gICAgdGFza3NDb21wbGV0ZWQ6IDEyNyxcbiAgICBzdWNjZXNzUmF0ZTogOTguNVxuICB9LFxuICB7XG4gICAgaWQ6IFwiYWdlbnQyXCIsXG4gICAgbmFtZTogXCLmlbDmja7liIbmnpDkuJPlrrZcIixcbiAgICB1cmw6IFwiaHR0cHM6Ly9leGFtcGxlLmNvbS9hZ2VudDJcIixcbiAgICB0eXBlOiBcIuWIhuaekFwiLFxuICAgIHN0YXR1czogXCLlv5nnooxcIixcbiAgICBsYXN0QWN0aXZlOiBcIuWImuWImlwiLFxuICAgIHRhc2tzQ29tcGxldGVkOiA4OSxcbiAgICBzdWNjZXNzUmF0ZTogOTYuMlxuICB9LFxuICB7XG4gICAgaWQ6IFwiYWdlbnQzXCIsXG4gICAgbmFtZTogXCLlhoXlrrnliJvkvZzliqnnkIZcIixcbiAgICB1cmw6IFwiaHR0cHM6Ly9leGFtcGxlLmNvbS9hZ2VudDNcIixcbiAgICB0eXBlOiBcIuWIm+S9nFwiLFxuICAgIHN0YXR1czogXCLlnKjnur9cIixcbiAgICBsYXN0QWN0aXZlOiBcIjXliIbpkp/liY1cIixcbiAgICB0YXNrc0NvbXBsZXRlZDogMTU2LFxuICAgIHN1Y2Nlc3NSYXRlOiA5NC44XG4gIH0sXG4gIHtcbiAgICBpZDogXCJhZ2VudDRcIixcbiAgICBuYW1lOiBcIuS7o+eggeWuoeafpeacuuWZqOS6ulwiLFxuICAgIHVybDogXCJodHRwczovL2V4YW1wbGUuY29tL2FnZW50NFwiLFxuICAgIHR5cGU6IFwi5byA5Y+RXCIsXG4gICAgc3RhdHVzOiBcIuemu+e6v1wiLFxuICAgIGxhc3RBY3RpdmU6IFwiMeWwj+aXtuWJjVwiLFxuICAgIHRhc2tzQ29tcGxldGVkOiA3MyxcbiAgICBzdWNjZXNzUmF0ZTogOTkuMVxuICB9LFxuXVxuXG5jb25zdCBkYXNoYm9hcmRTdGF0cyA9IFtcbiAge1xuICAgIHRpdGxlOiBcIuaAu+S7o+eQhlwiLFxuICAgIHZhbHVlOiBcIjRcIixcbiAgICBzdWJ0aXRsZTogXCIz5Liq5Zyo57q/XCIsXG4gICAgY29sb3I6IFwiYmctYmx1ZS01MDBcIixcbiAgICB0cmVuZDogXCIrMlwiLFxuICAgIHRyZW5kRGlyZWN0aW9uOiBcInVwXCJcbiAgfSxcbiAge1xuICAgIHRpdGxlOiBcIkNQVeS9v+eUqOeOh1wiLFxuICAgIHZhbHVlOiBcIjQyLjglXCIsXG4gICAgc3VidGl0bGU6IFwi5bmz5Z2H6LSf6L29XCIsXG4gICAgY29sb3I6IFwiYmctZ3JlZW4tNTAwXCIsXG4gICAgdHJlbmQ6IFwiLTUuMiVcIixcbiAgICB0cmVuZERpcmVjdGlvbjogXCJkb3duXCJcbiAgfSxcbiAge1xuICAgIHRpdGxlOiBcIuWGheWtmOS9v+eUqFwiLFxuICAgIHZhbHVlOiBcIjY3LjMlXCIsXG4gICAgc3VidGl0bGU6IFwiOC4yR0IgLyAxMkdCXCIsXG4gICAgY29sb3I6IFwiYmctcHVycGxlLTUwMFwiLFxuICAgIHRyZW5kOiBcIiszLjElXCIsXG4gICAgdHJlbmREaXJlY3Rpb246IFwidXBcIlxuICB9LFxuICB7XG4gICAgdGl0bGU6IFwi5LuK5pel5Lu75YqhXCIsXG4gICAgdmFsdWU6IFwiNDQ1XCIsXG4gICAgc3VidGl0bGU6IFwi5bey5a6M5oiQXCIsXG4gICAgY29sb3I6IFwiYmctb3JhbmdlLTUwMFwiLFxuICAgIHRyZW5kOiBcIisyOFwiLFxuICAgIHRyZW5kRGlyZWN0aW9uOiBcInVwXCJcbiAgfSxcbl1cblxuY29uc3QgYWdlbnRBY3Rpdml0eSA9IFtcbiAge1xuICAgIGlkOiAxLFxuICAgIGFnZW50OiBcIuaZuuiDveWuouacjeWKqeaJi1wiLFxuICAgIGFjdGlvbjogXCLlpITnkIbnlKjmiLflkqjor6JcIixcbiAgICBzdGF0dXM6IFwi5bey5a6M5oiQXCIsXG4gICAgdGltZTogXCLliJrliJpcIixcbiAgICBjb2xvcjogXCJiZy1ncmVlbi01MDBcIixcbiAgICBkdXJhdGlvbjogXCIy5YiG6ZKfXCJcbiAgfSxcbiAge1xuICAgIGlkOiAyLFxuICAgIGFnZW50OiBcIuaVsOaNruWIhuaekOS4k+WutlwiLFxuICAgIGFjdGlvbjogXCLnlJ/miJDplIDllK7miqXlkYpcIixcbiAgICBzdGF0dXM6IFwi6L+b6KGM5LitXCIsXG4gICAgdGltZTogXCIz5YiG6ZKf5YmNXCIsXG4gICAgY29sb3I6IFwiYmctYmx1ZS01MDBcIixcbiAgICBkdXJhdGlvbjogXCLpooTorqE15YiG6ZKfXCJcbiAgfSxcbiAge1xuICAgIGlkOiAzLFxuICAgIGFnZW50OiBcIuWGheWuueWIm+S9nOWKqeeQhlwiLFxuICAgIGFjdGlvbjogXCLmkrDlhpnkuqflk4Hmj4/ov7BcIixcbiAgICBzdGF0dXM6IFwi5bey5a6M5oiQXCIsXG4gICAgdGltZTogXCI15YiG6ZKf5YmNXCIsXG4gICAgY29sb3I6IFwiYmctZ3JlZW4tNTAwXCIsXG4gICAgZHVyYXRpb246IFwiOOWIhumSn1wiXG4gIH0sXG4gIHtcbiAgICBpZDogNCxcbiAgICBhZ2VudDogXCLmmbrog73lrqLmnI3liqnmiYtcIixcbiAgICBhY3Rpb246IFwi5pu05paw55+l6K+G5bqTXCIsXG4gICAgc3RhdHVzOiBcIuW3suWujOaIkFwiLFxuICAgIHRpbWU6IFwiOOWIhumSn+WJjVwiLFxuICAgIGNvbG9yOiBcImJnLWdyZWVuLTUwMFwiLFxuICAgIGR1cmF0aW9uOiBcIjPliIbpkp9cIlxuICB9LFxuICB7XG4gICAgaWQ6IDUsXG4gICAgYWdlbnQ6IFwi5Luj56CB5a6h5p+l5py65Zmo5Lq6XCIsXG4gICAgYWN0aW9uOiBcIuS7o+eggei0qOmHj+ajgOafpVwiLFxuICAgIHN0YXR1czogXCLnrYnlvoXkuK1cIixcbiAgICB0aW1lOiBcIjEy5YiG6ZKf5YmNXCIsXG4gICAgY29sb3I6IFwiYmcteWVsbG93LTUwMFwiLFxuICAgIGR1cmF0aW9uOiBcIuW+heWkhOeQhlwiXG4gIH0sXG5dXG5cbi8vIFBlcmZvcm1hbmNlIG1ldHJpY3MgZm9yIGNoYXJ0c1xuY29uc3QgcGVyZm9ybWFuY2VEYXRhID0ge1xuICBzeXN0ZW1Mb2FkOiBbXG4gICAgeyB0aW1lOiBcIjAwOjAwXCIsIGNwdTogMzUsIG1lbW9yeTogNjIsIG5ldHdvcms6IDQ1IH0sXG4gICAgeyB0aW1lOiBcIjA0OjAwXCIsIGNwdTogMjgsIG1lbW9yeTogNTgsIG5ldHdvcms6IDM4IH0sXG4gICAgeyB0aW1lOiBcIjA4OjAwXCIsIGNwdTogNDIsIG1lbW9yeTogNjUsIG5ldHdvcms6IDUyIH0sXG4gICAgeyB0aW1lOiBcIjEyOjAwXCIsIGNwdTogNDgsIG1lbW9yeTogNzEsIG5ldHdvcms6IDYxIH0sXG4gICAgeyB0aW1lOiBcIjE2OjAwXCIsIGNwdTogMzgsIG1lbW9yeTogNjcsIG5ldHdvcms6IDQ3IH0sXG4gICAgeyB0aW1lOiBcIjIwOjAwXCIsIGNwdTogMzMsIG1lbW9yeTogNjMsIG5ldHdvcms6IDQyIH0sXG4gIF0sXG4gIGFnZW50UGVyZm9ybWFuY2U6IFtcbiAgICB7IG5hbWU6IFwi5pm66IO95a6i5pyN5Yqp5omLXCIsIGNvbXBsZXRlZDogMTI3LCBzdWNjZXNzOiA5OC41LCBhdmdUaW1lOiAzLjIgfSxcbiAgICB7IG5hbWU6IFwi5pWw5o2u5YiG5p6Q5LiT5a62XCIsIGNvbXBsZXRlZDogODksIHN1Y2Nlc3M6IDk2LjIsIGF2Z1RpbWU6IDEyLjUgfSxcbiAgICB7IG5hbWU6IFwi5YaF5a655Yib5L2c5Yqp55CGXCIsIGNvbXBsZXRlZDogMTU2LCBzdWNjZXNzOiA5NC44LCBhdmdUaW1lOiA4LjcgfSxcbiAgICB7IG5hbWU6IFwi5Luj56CB5a6h5p+l5py65Zmo5Lq6XCIsIGNvbXBsZXRlZDogNzMsIHN1Y2Nlc3M6IDk5LjEsIGF2Z1RpbWU6IDE1LjMgfSxcbiAgXSxcbiAgdGFza0Rpc3RyaWJ1dGlvbjogW1xuICAgIHsgY2F0ZWdvcnk6IFwi5a6i5pyN5ZKo6K+iXCIsIGNvdW50OiAxODUsIHBlcmNlbnRhZ2U6IDQxLjYgfSxcbiAgICB7IGNhdGVnb3J5OiBcIuaVsOaNruWIhuaekFwiLCBjb3VudDogODksIHBlcmNlbnRhZ2U6IDIwLjAgfSxcbiAgICB7IGNhdGVnb3J5OiBcIuWGheWuueWIm+S9nFwiLCBjb3VudDogMTU2LCBwZXJjZW50YWdlOiAzNS4xIH0sXG4gICAgeyBjYXRlZ29yeTogXCLku6PnoIHlrqHmn6VcIiwgY291bnQ6IDE1LCBwZXJjZW50YWdlOiAzLjMgfSxcbiAgXVxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEYXNoYm9hcmRQYWdlKCkge1xuICBjb25zdCBbc2VsZWN0ZWRWaWV3LCBzZXRTZWxlY3RlZFZpZXddID0gdXNlU3RhdGU8c3RyaW5nPihcImhvbWVcIikgLy8gQ2hhbmdlZCBmcm9tIHNlbGVjdGVkQWdlbnQgdG8gc2VsZWN0ZWRWaWV3IGZvciBob21lL2FnZW50IHZpZXdzXG4gIGNvbnN0IFt1c2VybmFtZSwgc2V0VXNlcm5hbWVdID0gdXNlU3RhdGUoXCJcIilcbiAgY29uc3QgW3NpZGViYXJDb2xsYXBzZWQsIHNldFNpZGViYXJDb2xsYXBzZWRdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFttb2JpbGVNZW51T3Blbiwgc2V0TW9iaWxlTWVudU9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFt0b3VjaFN0YXJ0WCwgc2V0VG91Y2hTdGFydFhdID0gdXNlU3RhdGU8bnVtYmVyIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW3RvdWNoQ3VycmVudFgsIHNldFRvdWNoQ3VycmVudFhdID0gdXNlU3RhdGU8bnVtYmVyIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbaWZyYW1lRXJyb3IsIHNldElmcmFtZUVycm9yXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaXNMb2dnZWRJbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwiaXNMb2dnZWRJblwiKVxuICAgIGNvbnN0IHN0b3JlZFVzZXJuYW1lID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJ1c2VybmFtZVwiKVxuXG4gICAgaWYgKCFpc0xvZ2dlZEluKSB7XG4gICAgICByb3V0ZXIucHVzaChcIi9cIilcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIGlmIChzdG9yZWRVc2VybmFtZSkge1xuICAgICAgc2V0VXNlcm5hbWUoc3RvcmVkVXNlcm5hbWUpXG4gICAgfVxuICB9LCBbcm91dGVyXSlcblxuICAvLyBFbmhhbmNlZCBtb2JpbGUgbWVudSBhY2Nlc3NpYmlsaXR5IGFuZCBrZXlib2FyZCBzdXBwb3J0XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlS2V5RG93biA9IChlOiBLZXlib2FyZEV2ZW50KSA9PiB7XG4gICAgICBpZiAoZS5rZXkgPT09ICdFc2NhcGUnICYmIG1vYmlsZU1lbnVPcGVuKSB7XG4gICAgICAgIHNldE1vYmlsZU1lbnVPcGVuKGZhbHNlKVxuICAgICAgfVxuICAgIH1cblxuICAgIGNvbnN0IGhhbmRsZVJlc2l6ZSA9ICgpID0+IHtcbiAgICAgIC8vIENsb3NlIG1vYmlsZSBtZW51IHdoZW4gc3dpdGNoaW5nIHRvIGRlc2t0b3Agdmlld1xuICAgICAgaWYgKHdpbmRvdy5pbm5lcldpZHRoID49IDEwMjQgJiYgbW9iaWxlTWVudU9wZW4pIHtcbiAgICAgICAgc2V0TW9iaWxlTWVudU9wZW4oZmFsc2UpXG4gICAgICB9XG4gICAgfVxuXG4gICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGhhbmRsZUtleURvd24pXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIGhhbmRsZVJlc2l6ZSlcblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdrZXlkb3duJywgaGFuZGxlS2V5RG93bilcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCBoYW5kbGVSZXNpemUpXG4gICAgfVxuICB9LCBbbW9iaWxlTWVudU9wZW5dKVxuXG4gIGNvbnN0IGhhbmRsZUxvZ291dCA9ICgpID0+IHtcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShcImlzTG9nZ2VkSW5cIilcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShcInVzZXJuYW1lXCIpXG4gICAgcm91dGVyLnB1c2goXCIvXCIpXG4gIH1cblxuICBjb25zdCB0b2dnbGVTaWRlYmFyID0gKCkgPT4ge1xuICAgIHNldFNpZGViYXJDb2xsYXBzZWQoIXNpZGViYXJDb2xsYXBzZWQpXG4gIH1cblxuICBjb25zdCB0b2dnbGVNb2JpbGVNZW51ID0gKCkgPT4ge1xuICAgIHNldE1vYmlsZU1lbnVPcGVuKCFtb2JpbGVNZW51T3BlbilcbiAgfVxuXG4gIC8vIEVuaGFuY2VkIHRvdWNoIGhhbmRsaW5nIGZvciBtb2JpbGUgbWVudVxuICBjb25zdCBoYW5kbGVUb3VjaFN0YXJ0ID0gKGU6IFJlYWN0LlRvdWNoRXZlbnQpID0+IHtcbiAgICBzZXRUb3VjaFN0YXJ0WChlLnRvdWNoZXNbMF0uY2xpZW50WClcbiAgICBzZXRUb3VjaEN1cnJlbnRYKGUudG91Y2hlc1swXS5jbGllbnRYKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlVG91Y2hNb3ZlID0gKGU6IFJlYWN0LlRvdWNoRXZlbnQpID0+IHtcbiAgICBpZiAodG91Y2hTdGFydFggPT09IG51bGwpIHJldHVyblxuICAgIHNldFRvdWNoQ3VycmVudFgoZS50b3VjaGVzWzBdLmNsaWVudFgpXG4gIH1cblxuICBjb25zdCBoYW5kbGVUb3VjaEVuZCA9ICgpID0+IHtcbiAgICBpZiAodG91Y2hTdGFydFggPT09IG51bGwgfHwgdG91Y2hDdXJyZW50WCA9PT0gbnVsbCkgcmV0dXJuXG5cbiAgICBjb25zdCBkZWx0YVggPSB0b3VjaEN1cnJlbnRYIC0gdG91Y2hTdGFydFhcbiAgICBjb25zdCB0aHJlc2hvbGQgPSA1MCAvLyBNaW5pbXVtIHN3aXBlIGRpc3RhbmNlXG5cbiAgICAvLyBTd2lwZSByaWdodCB0byBvcGVuIG1lbnUgKHdoZW4gY2xvc2VkKVxuICAgIGlmIChkZWx0YVggPiB0aHJlc2hvbGQgJiYgIW1vYmlsZU1lbnVPcGVuKSB7XG4gICAgICBzZXRNb2JpbGVNZW51T3Blbih0cnVlKVxuICAgIH1cbiAgICAvLyBTd2lwZSBsZWZ0IHRvIGNsb3NlIG1lbnUgKHdoZW4gb3BlbilcbiAgICBlbHNlIGlmIChkZWx0YVggPCAtdGhyZXNob2xkICYmIG1vYmlsZU1lbnVPcGVuKSB7XG4gICAgICBzZXRNb2JpbGVNZW51T3BlbihmYWxzZSlcbiAgICB9XG5cbiAgICBzZXRUb3VjaFN0YXJ0WChudWxsKVxuICAgIHNldFRvdWNoQ3VycmVudFgobnVsbClcbiAgfVxuXG4gIC8vIENsb3NlIG1vYmlsZSBtZW51IHdoZW4gY2xpY2tpbmcgb3V0c2lkZVxuICBjb25zdCBoYW5kbGVCYWNrZHJvcENsaWNrID0gKGU6IFJlYWN0Lk1vdXNlRXZlbnQpID0+IHtcbiAgICBlLnN0b3BQcm9wYWdhdGlvbigpXG4gICAgc2V0TW9iaWxlTWVudU9wZW4oZmFsc2UpXG4gIH1cblxuICBjb25zdCBoYW5kbGVWaWV3U2VsZWN0ID0gKHZpZXc6IHN0cmluZykgPT4ge1xuICAgIC8vIFJlbmFtZWQgZnJvbSBoYW5kbGVBZ2VudFNlbGVjdCB0byBoYW5kbGVWaWV3U2VsZWN0XG4gICAgaWYgKHZpZXcgPT09IFwiaG9tZVwiKSB7XG4gICAgICBzZXRTZWxlY3RlZFZpZXcoXCJob21lXCIpXG4gICAgICBzZXRNb2JpbGVNZW51T3BlbihmYWxzZSlcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIHNldElzTG9hZGluZyh0cnVlKVxuICAgIHNldElmcmFtZUVycm9yKGZhbHNlKVxuICAgIHNldFNlbGVjdGVkVmlldyh2aWV3KVxuICAgIHNldE1vYmlsZU1lbnVPcGVuKGZhbHNlKVxuICAgIHNldFRpbWVvdXQoKCkgPT4gc2V0SXNMb2FkaW5nKGZhbHNlKSwgMTAwMClcbiAgfVxuXG5cblxuICBjb25zdCBzZWxlY3RlZEFnZW50RGF0YSA9IGFnZW50cy5maW5kKChhZ2VudCkgPT4gYWdlbnQuaWQgPT09IHNlbGVjdGVkVmlldylcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLXNsYXRlLTUwIGZsZXhcIj5cbiAgICAgIHsvKiBFbmhhbmNlZCBtb2JpbGUgbWVudSBiYWNrZHJvcCB3aXRoIHNtb290aCBmYWRlIGFuaW1hdGlvbiAqL31cbiAgICAgIDxkaXZcbiAgICAgICAgY2xhc3NOYW1lPXtgXG4gICAgICAgICAgZml4ZWQgaW5zZXQtMCB6LTQwIGxnOmhpZGRlbiB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXRcbiAgICAgICAgICAke21vYmlsZU1lbnVPcGVuXG4gICAgICAgICAgICA/IFwiYmctYmxhY2sgYmctb3BhY2l0eS01MCBiYWNrZHJvcC1ibHVyLXNtIHZpc2libGVcIlxuICAgICAgICAgICAgOiBcImJnLXRyYW5zcGFyZW50IGludmlzaWJsZVwiXG4gICAgICAgICAgfVxuICAgICAgICBgfVxuICAgICAgICBvbkNsaWNrPXtoYW5kbGVCYWNrZHJvcENsaWNrfVxuICAgICAgICBvblRvdWNoU3RhcnQ9e2hhbmRsZVRvdWNoU3RhcnR9XG4gICAgICAgIG9uVG91Y2hNb3ZlPXtoYW5kbGVUb3VjaE1vdmV9XG4gICAgICAgIG9uVG91Y2hFbmQ9e2hhbmRsZVRvdWNoRW5kfVxuICAgICAgLz5cblxuICAgICAgPGFzaWRlXG4gICAgICAgIGNsYXNzTmFtZT17YFxuICAgICAgICAgICR7bW9iaWxlTWVudU9wZW4gPyBcInRyYW5zbGF0ZS14LTAgc2hhZG93LTJ4bFwiIDogXCItdHJhbnNsYXRlLXgtZnVsbCBzaGFkb3ctbm9uZVwifVxuICAgICAgICAgIGxnOnRyYW5zbGF0ZS14LTAgbGc6c2hhZG93LW5vbmVcbiAgICAgICAgICBmaXhlZCBsZzpyZWxhdGl2ZVxuICAgICAgICAgIHotNTAgbGc6ei1hdXRvXG4gICAgICAgICAgYmctc2xhdGUtOTAwXG4gICAgICAgICAgYm9yZGVyLXIgYm9yZGVyLXNsYXRlLTcwMFxuICAgICAgICAgIGZsZXggZmxleC1jb2xcbiAgICAgICAgICB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZWFzZS1vdXRcbiAgICAgICAgICAke3NpZGViYXJDb2xsYXBzZWQgPyBcImxnOnctMTZcIiA6IFwibGc6dy04MFwifVxuICAgICAgICAgIHctODBcbiAgICAgICAgICBoLXNjcmVlblxuICAgICAgICAgIGxnOnRyYW5zZm9ybS1ub25lXG4gICAgICAgIGB9XG4gICAgICAgIG9uVG91Y2hTdGFydD17aGFuZGxlVG91Y2hTdGFydH1cbiAgICAgICAgb25Ub3VjaE1vdmU9e2hhbmRsZVRvdWNoTW92ZX1cbiAgICAgICAgb25Ub3VjaEVuZD17aGFuZGxlVG91Y2hFbmR9XG4gICAgICA+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgYm9yZGVyLWIgYm9yZGVyLXNsYXRlLTcwMC81MCAke3NpZGViYXJDb2xsYXBzZWQgPyAncC0yJyA6ICdwLTMnfWB9PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgbWItMyAke3NpZGViYXJDb2xsYXBzZWQgPyAnanVzdGlmeS1jZW50ZXInIDogJ2p1c3RpZnktYmV0d2Vlbid9YH0+XG4gICAgICAgICAgICB7IXNpZGViYXJDb2xsYXBzZWQgJiYgPGgxIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlXCI+QWdlbnQg566h55CG57O757ufPC9oMT59XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlU2lkZWJhcn1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoaWRkZW4gbGc6ZmxleCB0ZXh0LXNsYXRlLTQwMCBob3ZlcjpiZy1zbGF0ZS04MDAvODAgaG92ZXI6dGV4dC13aGl0ZSByb3VuZGVkLWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7c2lkZWJhckNvbGxhcHNlZCA/IDxDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+IDogPENoZXZyb25MZWZ0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPn1cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlTW9iaWxlTWVudX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJsZzpoaWRkZW4gdGV4dC1zbGF0ZS00MDAgaG92ZXI6Ymctc2xhdGUtODAwLzgwIGhvdmVyOnRleHQtd2hpdGUgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6c2NhbGUtMTA1IGFjdGl2ZTpzY2FsZS05NVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTIwMCBob3Zlcjpyb3RhdGUtOTBcIj5cbiAgICAgICAgICAgICAgICAgIDxYIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFVzZXIgcHJvZmlsZSBzZWN0aW9uIC0gY29tcGFjdCBkZXNpZ24gbWF0Y2hpbmcgaG9tZXBhZ2UgKi99XG4gICAgICAgICAge3NpZGViYXJDb2xsYXBzZWQgPyAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgbWItM1wiPlxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy03IGgtNyBiZy1ibHVlLTYwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGN1cnNvci1wb2ludGVyIGhvdmVyOmJnLWJsdWUtNzAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBob3ZlcjpzY2FsZS0xMDVcIlxuICAgICAgICAgICAgICAgIHRpdGxlPXtgJHt1c2VybmFtZSB8fCBcIuaJi+ezuyBBZ2VudFwifSAtIOeuoeeQhuWRmGB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8VXNlciBjbGFzc05hbWU9XCJoLTMuNSB3LTMuNSB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBwLTIuNSBiZy1zbGF0ZS04MDAvNjAgcm91bmRlZC1sZyBtYi0zIGJvcmRlciBib3JkZXItc2xhdGUtNzAwLzMwXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy03IGgtNyBiZy1ibHVlLTYwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwiaC0zLjUgdy0zLjUgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4tdy0wXCI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlIHRydW5jYXRlXCI+e3VzZXJuYW1lIHx8IFwi5omL57O7IEFnZW50XCJ9PC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1zbGF0ZS00MDBcIj7nrqHnkIblkZg8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPG5hdiBjbGFzc05hbWU9e2BmbGV4LTEgJHtzaWRlYmFyQ29sbGFwc2VkID8gJ3AtMicgOiAncC0zJ30gb3ZlcmZsb3cteS1hdXRvYH0+XG4gICAgICAgICAgey8qIFByaW1hcnkgTmF2aWdhdGlvbiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMVwiPlxuICAgICAgICAgICAgey8qIERhc2hib2FyZCBIb21lIC0gUHJpbWFyeSBMZXZlbCAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVWaWV3U2VsZWN0KFwiaG9tZVwiKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgICAgIHctZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciByb3VuZGVkLWxnIHRleHQtc20gZm9udC1tZWRpdW1cbiAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBtaW4taC1bMzZweF1cbiAgICAgICAgICAgICAgICAgICR7c2lkZWJhckNvbGxhcHNlZCA/ICdqdXN0aWZ5LWNlbnRlciBweC0yIHB5LTIuNScgOiAnZ2FwLTIuNSBweC0yLjUgcHktMi41J31cbiAgICAgICAgICAgICAgICAgICR7XG4gICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkVmlldyA9PT0gXCJob21lXCJcbiAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBzaGFkb3ctbWQgYm9yZGVyIGJvcmRlci1ibHVlLTUwMC8zMFwiXG4gICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtc2xhdGUtMzAwIGhvdmVyOmJnLXNsYXRlLTgwMC82MCBob3Zlcjp0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBgfVxuICAgICAgICAgICAgICAgIHRpdGxlPXtzaWRlYmFyQ29sbGFwc2VkID8gXCLpppbpobVcIiA6IHVuZGVmaW5lZH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxIb21lIGNsYXNzTmFtZT1cImgtNCB3LTQgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgICAgICAgeyFzaWRlYmFyQ29sbGFwc2VkICYmIDxzcGFuPummlumhtTwvc3Bhbj59XG4gICAgICAgICAgICAgICAgeyFzaWRlYmFyQ29sbGFwc2VkICYmIHNlbGVjdGVkVmlldyA9PT0gXCJob21lXCIgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC1hdXRvIHctMS41IGgtMS41IGJnLXdoaXRlIHJvdW5kZWQtZnVsbFwiIC8+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEFJIEFnZW50cyBTZWN0aW9uIC0gUHJpbWFyeSBMZXZlbCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHQtMyByZWxhdGl2ZSBncm91cFwiPlxuICAgICAgICAgICAgICB7c2lkZWJhckNvbGxhcHNlZCA/IChcbiAgICAgICAgICAgICAgICAvKiBDb2xsYXBzZWQ6IFNob3cgc2VjdGlvbiBpY29uIHdpdGggaG92ZXIgdG9vbHRpcCAqL1xuICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YFxuICAgICAgICAgICAgICAgICAgICAgIHctZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciByb3VuZGVkLWxnIHRleHQtc20gZm9udC1tZWRpdW1cbiAgICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgbWluLWgtWzM2cHhdIHB4LTIgcHktMi41IGN1cnNvci1kZWZhdWx0XG4gICAgICAgICAgICAgICAgICAgICAgbGc6aG92ZXI6Ymctc2xhdGUtODAwLzYwIGxnOmhvdmVyOnRleHQtd2hpdGVcbiAgICAgICAgICAgICAgICAgICAgICAke1xuICAgICAgICAgICAgICAgICAgICAgICAgYWdlbnRzLnNvbWUoYWdlbnQgPT4gc2VsZWN0ZWRWaWV3ID09PSBhZ2VudC5pZClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLWJsdWUtNjAwIHRleHQtd2hpdGUgc2hhZG93LW1kIGJvcmRlciBib3JkZXItYmx1ZS01MDAvMzBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1zbGF0ZS0zMDBcIlxuICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgYH1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPEJyYWluIGNsYXNzTmFtZT1cImgtNCB3LTQgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIEhvdmVyIFRvb2x0aXAvRHJvcGRvd24gZm9yIEFJIEFnZW50cyAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC1mdWxsIHRvcC0wIG1sLTMgb3BhY2l0eS0wIGludmlzaWJsZSBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCBncm91cC1ob3Zlcjp2aXNpYmxlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBlYXNlLW91dCB6LTUwIHBvaW50ZXItZXZlbnRzLW5vbmUgZ3JvdXAtaG92ZXI6cG9pbnRlci1ldmVudHMtYXV0byB0cmFuc2Zvcm0gdHJhbnNsYXRlLXgtMSBncm91cC1ob3Zlcjp0cmFuc2xhdGUteC0wXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc2xhdGUtODAwLzk1IGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci1zbGF0ZS03MDAvNjAgcm91bmRlZC1sZyBzaGFkb3ctMnhsIG1pbi13LVsyMjBweF0gcHktMiBiZWZvcmU6Y29udGVudC1bJyddIGJlZm9yZTphYnNvbHV0ZSBiZWZvcmU6bGVmdC1bLTZweF0gYmVmb3JlOnRvcC00IGJlZm9yZTp3LTAgYmVmb3JlOmgtMCBiZWZvcmU6Ym9yZGVyLXQtWzZweF0gYmVmb3JlOmJvcmRlci1iLVs2cHhdIGJlZm9yZTpib3JkZXItci1bNnB4XSBiZWZvcmU6Ym9yZGVyLXRyYW5zcGFyZW50IGJlZm9yZTpib3JkZXItci1zbGF0ZS04MDAvOTVcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTMgcHktMiBib3JkZXItYiBib3JkZXItc2xhdGUtNzAwLzUwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LXNlbWlib2xkIHRleHQtc2xhdGUtMzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlciBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QnJhaW4gY2xhc3NOYW1lPVwiaC0zIHctMyB0ZXh0LWJsdWUtNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgQUkg5LiT5a62XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHktMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2FnZW50cy5tYXAoKGFnZW50KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2FnZW50LmlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVZpZXdTZWxlY3QoYWdlbnQuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdy1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yLjUgcHgtMyBweS0yLjUgdGV4dC1zbSBmb250LW1lZGl1bVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGhvdmVyOmJnLXNsYXRlLTcwMC84MCByb3VuZGVkLW1kIG14LTFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkVmlldyA9PT0gYWdlbnQuaWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBzaGFkb3ctbWRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXNsYXRlLTMwMCBob3Zlcjp0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTIgaC0yIHJvdW5kZWQtZnVsbCBmbGV4LXNocmluay0wICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhZ2VudC5zdGF0dXMgPT09ICflnKjnur8nID8gJ2JnLWdyZWVuLTUwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWdlbnQuc3RhdHVzID09PSAn5b+Z56KMJyA/ICdiZy15ZWxsb3ctNTAwJyA6ICdiZy1ncmF5LTQwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidHJ1bmNhdGVcIj57YWdlbnQubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkVmlldyA9PT0gYWdlbnQuaWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC1hdXRvIHctMS41IGgtMS41IGJnLXdoaXRlIHJvdW5kZWQtZnVsbFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAvKiBFeHBhbmRlZDogU2hvdyBzZWN0aW9uIGhlYWRlciBhbmQgYWxsIGFnZW50IGl0ZW1zICovXG4gICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtMi41IHB5LTEuNSBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdGV4dC1zbGF0ZS00MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEJyYWluIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIEFJIOS4k+WutlxuICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBTZWNvbmRhcnkgTGV2ZWwgLSBBZ2VudCBJdGVtcyAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0wLjVcIj5cbiAgICAgICAgICAgICAgICAgICAge2FnZW50cy5tYXAoKGFnZW50KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAga2V5PXthZ2VudC5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVZpZXdTZWxlY3QoYWdlbnQuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHctZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciByb3VuZGVkLWxnIHRleHQtc20gZm9udC1tZWRpdW1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIG1pbi1oLVszNHB4XSBnYXAtMi41IHB4LTMgcHktMiBtbC0xXG4gICAgICAgICAgICAgICAgICAgICAgICAgICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRWaWV3ID09PSBhZ2VudC5pZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLWJsdWUtNjAwIHRleHQtd2hpdGUgc2hhZG93LW1kIGJvcmRlciBib3JkZXItYmx1ZS01MDAvMzBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtc2xhdGUtMzAwIGhvdmVyOmJnLXNsYXRlLTgwMC82MCBob3Zlcjp0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgYH1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctMiBoLTIgcm91bmRlZC1mdWxsIGZsZXgtc2hyaW5rLTAgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYWdlbnQuc3RhdHVzID09PSAn5Zyo57q/JyA/ICdiZy1ncmVlbi01MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYWdlbnQuc3RhdHVzID09PSAn5b+Z56KMJyA/ICdiZy15ZWxsb3ctNTAwJyA6ICdiZy1ncmF5LTQwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRydW5jYXRlXCI+e2FnZW50Lm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkVmlldyA9PT0gYWdlbnQuaWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLWF1dG8gdy0xLjUgaC0xLjUgYmctd2hpdGUgcm91bmRlZC1mdWxsXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEFuYWx5dGljcyBTZWN0aW9uIC0gUHJpbWFyeSBMZXZlbCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHQtMyByZWxhdGl2ZSBncm91cFwiPlxuICAgICAgICAgICAgICB7c2lkZWJhckNvbGxhcHNlZCA/IChcbiAgICAgICAgICAgICAgICAvKiBDb2xsYXBzZWQ6IFNob3cgc2VjdGlvbiBpY29uIHdpdGggaG92ZXIgdG9vbHRpcCAqL1xuICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YFxuICAgICAgICAgICAgICAgICAgICAgIHctZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciByb3VuZGVkLWxnIHRleHQtc20gZm9udC1tZWRpdW1cbiAgICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgbWluLWgtWzM2cHhdIHB4LTIgcHktMi41IGN1cnNvci1kZWZhdWx0XG4gICAgICAgICAgICAgICAgICAgICAgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkVmlldyA9PT0gXCJhbmFseXRpY3NcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBzaGFkb3ctbWQgYm9yZGVyIGJvcmRlci1ibHVlLTUwMC8zMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXNsYXRlLTMwMCBob3ZlcjpiZy1zbGF0ZS04MDAvNjAgaG92ZXI6dGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBgfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8QmFyQ2hhcnQzIGNsYXNzTmFtZT1cImgtNCB3LTQgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIEhvdmVyIFRvb2x0aXAvRHJvcGRvd24gZm9yIEFuYWx5dGljcyAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC1mdWxsIHRvcC0wIG1sLTMgb3BhY2l0eS0wIGludmlzaWJsZSBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCBncm91cC1ob3Zlcjp2aXNpYmxlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBlYXNlLW91dCB6LTUwIHBvaW50ZXItZXZlbnRzLW5vbmUgZ3JvdXAtaG92ZXI6cG9pbnRlci1ldmVudHMtYXV0byB0cmFuc2Zvcm0gdHJhbnNsYXRlLXgtMSBncm91cC1ob3Zlcjp0cmFuc2xhdGUteC0wXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc2xhdGUtODAwLzk1IGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci1zbGF0ZS03MDAvNjAgcm91bmRlZC1sZyBzaGFkb3ctMnhsIG1pbi13LVsyMjBweF0gcHktMiBiZWZvcmU6Y29udGVudC1bJyddIGJlZm9yZTphYnNvbHV0ZSBiZWZvcmU6bGVmdC1bLTZweF0gYmVmb3JlOnRvcC00IGJlZm9yZTp3LTAgYmVmb3JlOmgtMCBiZWZvcmU6Ym9yZGVyLXQtWzZweF0gYmVmb3JlOmJvcmRlci1iLVs2cHhdIGJlZm9yZTpib3JkZXItci1bNnB4XSBiZWZvcmU6Ym9yZGVyLXRyYW5zcGFyZW50IGJlZm9yZTpib3JkZXItci1zbGF0ZS04MDAvOTVcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTMgcHktMiBib3JkZXItYiBib3JkZXItc2xhdGUtNzAwLzUwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LXNlbWlib2xkIHRleHQtc2xhdGUtMzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlciBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFyQ2hhcnQzIGNsYXNzTmFtZT1cImgtMyB3LTMgdGV4dC1wdXJwbGUtNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAg5YiG5p6Q5bel5YW3XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHktMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVWaWV3U2VsZWN0KFwiYW5hbHl0aWNzXCIpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3LWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIuNSBweC0zIHB5LTIuNSB0ZXh0LXNtIGZvbnQtbWVkaXVtXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGhvdmVyOmJnLXNsYXRlLTcwMC84MCByb3VuZGVkLW1kIG14LTFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRWaWV3ID09PSBcImFuYWx5dGljc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHNoYWRvdy1tZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXNsYXRlLTMwMCBob3Zlcjp0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGB9XG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5lQ2hhcnQgY2xhc3NOYW1lPVwiaC0zLjUgdy0zLjUgZmxleC1zaHJpbmstMCB0ZXh0LXB1cnBsZS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0cnVuY2F0ZVwiPuaAp+iDveWIhuaekDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkVmlldyA9PT0gXCJhbmFseXRpY3NcIiAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC1hdXRvIHctMS41IGgtMS41IGJnLXdoaXRlIHJvdW5kZWQtZnVsbFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAvKiBFeHBhbmRlZDogU2hvdyBzZWN0aW9uIGhlYWRlciBhbmQgYW5hbHl0aWNzIGl0ZW1zICovXG4gICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtMi41IHB5LTEuNSBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdGV4dC1zbGF0ZS00MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEJhckNoYXJ0MyBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICDliIbmnpDlt6XlhbdcbiAgICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogU2Vjb25kYXJ5IExldmVsIC0gQW5hbHl0aWNzIEl0ZW1zICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTAuNVwiPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVmlld1NlbGVjdChcImFuYWx5dGljc1wiKX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgICAgICAgICAgIHctZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciByb3VuZGVkLWxnIHRleHQtc20gZm9udC1tZWRpdW1cbiAgICAgICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBtaW4taC1bMzRweF0gZ2FwLTIuNSBweC0zIHB5LTIgbWwtMVxuICAgICAgICAgICAgICAgICAgICAgICAgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRWaWV3ID09PSBcImFuYWx5dGljc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLWJsdWUtNjAwIHRleHQtd2hpdGUgc2hhZG93LW1kIGJvcmRlciBib3JkZXItYmx1ZS01MDAvMzBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXNsYXRlLTMwMCBob3ZlcjpiZy1zbGF0ZS04MDAvNjAgaG92ZXI6dGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgYH1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxMaW5lQ2hhcnQgY2xhc3NOYW1lPVwiaC0zLjUgdy0zLjUgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidHJ1bmNhdGVcIj7mgKfog73liIbmnpA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkVmlldyA9PT0gXCJhbmFseXRpY3NcIiAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLWF1dG8gdy0xLjUgaC0xLjUgYmctd2hpdGUgcm91bmRlZC1mdWxsXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvbmF2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgYm9yZGVyLXQgYm9yZGVyLXNsYXRlLTcwMC81MCAke3NpZGViYXJDb2xsYXBzZWQgPyAncC0yJyA6ICdwLTMnfWB9PlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVMb2dvdXR9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgdy1mdWxsIHRleHQtc2xhdGUtMzAwIGhvdmVyOmJnLXNsYXRlLTgwMC82MCBob3Zlcjp0ZXh0LXdoaXRlIHJvdW5kZWQtbGdcbiAgICAgICAgICAgICAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIG1pbi1oLVszNnB4XVxuICAgICAgICAgICAgICAke3NpZGViYXJDb2xsYXBzZWQgPyBcImp1c3RpZnktY2VudGVyIHB4LTIgcHktMlwiIDogXCJqdXN0aWZ5LXN0YXJ0IHB4LTIuNSBweS0yXCJ9XG4gICAgICAgICAgICBgfVxuICAgICAgICAgICAgdGl0bGU9e3NpZGViYXJDb2xsYXBzZWQgPyBcIumAgOWHuueZu+W9lVwiIDogdW5kZWZpbmVkfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxMb2dPdXQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICB7IXNpZGViYXJDb2xsYXBzZWQgJiYgPHNwYW4gY2xhc3NOYW1lPVwibWwtMiB0ZXh0LXNtXCI+6YCA5Ye655m75b2VPC9zcGFuPn1cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2FzaWRlPlxuXG4gICAgICB7LyogRW5oYW5jZWQgbW9iaWxlIG1lbnUgYnV0dG9uIHdpdGggY29tcGFjdCBkZXNpZ24gbWF0Y2hpbmcgaG9tZXBhZ2UgKi99XG4gICAgICA8QnV0dG9uXG4gICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgIG9uQ2xpY2s9e3RvZ2dsZU1vYmlsZU1lbnV9XG4gICAgICAgIGNsYXNzTmFtZT17YFxuICAgICAgICAgIGxnOmhpZGRlbiBmaXhlZCB0b3AtMyBsZWZ0LTMgei0zMFxuICAgICAgICAgIGJnLXNsYXRlLTkwMC85NSBiYWNrZHJvcC1ibHVyLXNtIHRleHQtd2hpdGVcbiAgICAgICAgICBob3ZlcjpiZy1zbGF0ZS04MDAvOTAgYWN0aXZlOmJnLXNsYXRlLTcwMFxuICAgICAgICAgIGJvcmRlciBib3JkZXItc2xhdGUtNzAwLzQwIHJvdW5kZWQtbGdcbiAgICAgICAgICB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZWFzZS1pbi1vdXRcbiAgICAgICAgICBob3ZlcjpzY2FsZS0xMDUgYWN0aXZlOnNjYWxlLTk1XG4gICAgICAgICAgc2hhZG93LW1kIGhvdmVyOnNoYWRvdy1sZ1xuICAgICAgICAgIG1pbi1oLVszNnB4XSBtaW4tdy1bMzZweF1cbiAgICAgICAgICAke21vYmlsZU1lbnVPcGVuID8gJ2JnLXNsYXRlLTgwMC85MCBzY2FsZS0xMDUnIDogJyd9XG4gICAgICAgIGB9XG4gICAgICAgIG9uVG91Y2hTdGFydD17aGFuZGxlVG91Y2hTdGFydH1cbiAgICAgICAgb25Ub3VjaE1vdmU9e2hhbmRsZVRvdWNoTW92ZX1cbiAgICAgICAgb25Ub3VjaEVuZD17aGFuZGxlVG91Y2hFbmR9XG4gICAgICA+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMjAwICR7bW9iaWxlTWVudU9wZW4gPyAncm90YXRlLTkwJyA6ICcnfWB9PlxuICAgICAgICAgIDxNZW51IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvQnV0dG9uPlxuXG4gICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTEgcC0zIGxnOnAtNCBvdmVyZmxvdy1hdXRvXCI+XG4gICAgICAgIHtzZWxlY3RlZFZpZXcgPT09IFwiaG9tZVwiID8gKCAvLyBDaGFuZ2VkIGNvbmRpdGlvbiB0byBjaGVjayBmb3IgaG9tZSB2aWV3XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIHsvKiBXZWxjb21lIEhlYWRlciAtIENvbXBhY3QgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1zbGF0ZS0yMDAgcC00XCI+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LXNsYXRlLTkwMCBtYi0xXCI+5qyi6L+O5Zue5p2l77yMe3VzZXJuYW1lIHx8IFwi566h55CG5ZGYXCJ9PC9oMT5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXNsYXRlLTYwMFwiPkFJIEFnZW50IOeuoeeQhuS4reW/g+amguiniDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogRGFzaGJvYXJkIFN0YXRzIC0gQ29tcGFjdCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiB4bDpncmlkLWNvbHMtNCBnYXAtM1wiPlxuICAgICAgICAgICAgICB7ZGFzaGJvYXJkU3RhdHMubWFwKChzdGF0LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgIDxDYXJkIGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlci1zbGF0ZS0yMDAgaG92ZXI6c2hhZG93LWxnIHRyYW5zaXRpb24tc2hhZG93XCI+XG4gICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1zbGF0ZS02MDBcIj57c3RhdC50aXRsZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LXNsYXRlLTkwMCBtdC0xXCI+e3N0YXQudmFsdWV9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTUwMFwiPntzdGF0LnN1YnRpdGxlfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctMTAgaC0xMCAke3N0YXQuY29sb3J9IHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJgfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy01IGgtNSBiZy13aGl0ZSByb3VuZGVkIG9wYWNpdHktODBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIHtzdGF0LnRyZW5kICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTIgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQteHMgZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdC50cmVuZERpcmVjdGlvbiA9PT0gJ3VwJyA/ICd0ZXh0LWdyZWVuLTYwMCcgOiAndGV4dC1yZWQtNjAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7c3RhdC50cmVuZERpcmVjdGlvbiA9PT0gJ3VwJyA/ICfihpcnIDogJ+KGmCd9IHtzdGF0LnRyZW5kfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTUwMCBtbC0xXCI+dnMg5pio5aSpPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBDb25zb2xpZGF0ZWQgUGVyZm9ybWFuY2UgRGFzaGJvYXJkIC0gUmVzcG9uc2l2ZSBncmlkIGxheW91dCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiB4bDpncmlkLWNvbHMtMyBnYXAtM1wiPlxuICAgICAgICAgICAgICB7LyogQ29tYmluZWQgQWdlbnQgUGVyZm9ybWFuY2UgYW5kIFRhc2sgRGlzdHJpYnV0aW9uICovfVxuICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXItc2xhdGUtMjAwIGhvdmVyOnNoYWRvdy1sZyB0cmFuc2l0aW9uLXNoYWRvd1wiPlxuICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cInBiLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zbGF0ZS05MDAgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICA8QmFyQ2hhcnQzIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ibHVlLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIEFnZW50IOaAp+iDvVxuICAgICAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgIHtwZXJmb3JtYW5jZURhdGEuYWdlbnRQZXJmb3JtYW5jZS5zbGljZSgwLCAzKS5tYXAoKGFnZW50LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTIgYmctc2xhdGUtNTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1zbGF0ZS05MDAgdHJ1bmNhdGVcIj57YWdlbnQubmFtZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2xhdGUtNTAwXCI+5oiQ5Yqf546HOiB7YWdlbnQuc3VjY2Vzc30lPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LWJvbGQgdGV4dC1ibHVlLTYwMFwiPnthZ2VudC5jb21wbGV0ZWR9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTUwMFwiPuS7u+WKoTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgICAgICAgey8qIFN5c3RlbSBMb2FkIENoYXJ0IC0gQ29tcGFjdCAqL31cbiAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLXNsYXRlLTIwMCBob3ZlcjpzaGFkb3ctbGcgdHJhbnNpdGlvbi1zaGFkb3dcIj5cbiAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi0yXCI+XG4gICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtc2xhdGUtOTAwIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgPExpbmVDaGFydCBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtcHVycGxlLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIOezu+e7n+i0n+i9vVxuICAgICAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgIHtwZXJmb3JtYW5jZURhdGEuc3lzdGVtTG9hZC5zbGljZSgtMykubWFwKChsb2FkLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gdGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbGF0ZS02MDBcIj57bG9hZC50aW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc2xhdGUtOTAwIGZvbnQtbWVkaXVtXCI+Q1BVOiB7bG9hZC5jcHV9JTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBiZy1zbGF0ZS0yMDAgcm91bmRlZC1mdWxsIGgtMS41XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXB1cnBsZS02MDAgaC0xLjUgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiBgJHtsb2FkLmNwdX0lYCB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgICAgICB7LyogUmVjZW50IEFjdGl2aXR5IC0gQ29tcGFjdCAqL31cbiAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLXNsYXRlLTIwMCBtZDpjb2wtc3Bhbi0yIHhsOmNvbC1zcGFuLTFcIj5cbiAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi0yXCI+XG4gICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtc2xhdGUtOTAwIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgPEFjdGl2aXR5IGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1vcmFuZ2UtNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAg5pyA6L+R5rS75YqoXG4gICAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAge2FnZW50QWN0aXZpdHkuc2xpY2UoMCwgMykubWFwKChhY3Rpdml0eSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17YWN0aXZpdHkuaWR9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHAtMiBiZy1zbGF0ZS01MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTIgaC0yIHJvdW5kZWQtZnVsbCAke2FjdGl2aXR5LmNvbG9yfWB9PjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1zbGF0ZS05MDAgdHJ1bmNhdGVcIj57YWN0aXZpdHkuYWdlbnR9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTYwMCB0cnVuY2F0ZVwiPnthY3Rpdml0eS5hY3Rpb259PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPXtgdGV4dC14cyBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICBhY3Rpdml0eS5zdGF0dXMgPT09ICflt7LlrozmiJAnID8gJ3RleHQtZ3JlZW4tNjAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2aXR5LnN0YXR1cyA9PT0gJ+i/m+ihjOS4rScgPyAndGV4dC1ibHVlLTYwMCcgOiAndGV4dC15ZWxsb3ctNjAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7YWN0aXZpdHkuc3RhdHVzfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogQWdlbnQgU3RhdHVzIC0gU2luZ2xlIHJvdyBmb3IgYWxsIGFnZW50cyB3aXRoIGltcHJvdmVkIHJlc3BvbnNpdmUgbGF5b3V0ICovfVxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLXNsYXRlLTIwMFwiPlxuICAgICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi0yXCI+XG4gICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTkwMCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICA8VXNlcnMgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWluZGlnby02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgQWdlbnQg54q25oCB5oC76KeIXG4gICAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBzbTpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAge2FnZW50cy5tYXAoKGFnZW50KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXthZ2VudC5pZH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtMiBiZy1zbGF0ZS01MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTIgaC0yIHJvdW5kZWQtZnVsbCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICBhZ2VudC5zdGF0dXMgPT09ICflnKjnur8nID8gJ2JnLWdyZWVuLTUwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICBhZ2VudC5zdGF0dXMgPT09ICflv5nnoownID8gJ2JnLXllbGxvdy01MDAnIDogJ2JnLWdyYXktNDAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgfWB9PjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4tdy0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1zbGF0ZS05MDAgdHJ1bmNhdGVcIj57YWdlbnQubmFtZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1zbGF0ZS01MDBcIj57YWdlbnQudHlwZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1zbGF0ZS05MDBcIj57YWdlbnQudGFza3NDb21wbGV0ZWR9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImgtZnVsbCBiZy13aGl0ZSBib3JkZXItc2xhdGUtMjAwXCI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwiaC1mdWxsIHAtMFwiPlxuICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cImgtOCB3LTggYW5pbWF0ZS1zcGluIHRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTYwMFwiPuWKoOi9vSB7c2VsZWN0ZWRBZ2VudERhdGE/Lm5hbWV9Li4uPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkgOiBpZnJhbWVFcnJvciA/IChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBnYXAtNCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwiaC0xMiB3LTEyIHRleHQtcmVkLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbGF0ZS05MDAgZm9udC1tZWRpdW1cIj7liqDovb3lpLHotKU8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbGF0ZS02MDAgdGV4dC1zbSBtdC0xXCI+5peg5rOV5Yqg6L29IHtzZWxlY3RlZEFnZW50RGF0YT8ubmFtZX08L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9eygpID0+IGhhbmRsZVZpZXdTZWxlY3Qoc2VsZWN0ZWRWaWV3KX0gdmFyaWFudD1cIm91dGxpbmVcIiBzaXplPVwic21cIj5cbiAgICAgICAgICAgICAgICAgICAgICDph43or5VcbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IHNlbGVjdGVkQWdlbnREYXRhID8gKFxuICAgICAgICAgICAgICAgIDxpZnJhbWVcbiAgICAgICAgICAgICAgICAgIHNyYz17c2VsZWN0ZWRBZ2VudERhdGEudXJsfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBib3JkZXItMCByb3VuZGVkLWxnXCJcbiAgICAgICAgICAgICAgICAgIHRpdGxlPXtzZWxlY3RlZEFnZW50RGF0YS5uYW1lfVxuICAgICAgICAgICAgICAgICAgb25FcnJvcj17KCkgPT4gc2V0SWZyYW1lRXJyb3IodHJ1ZSl9XG4gICAgICAgICAgICAgICAgICBzYW5kYm94PVwiYWxsb3ctc2NyaXB0cyBhbGxvdy1zYW1lLW9yaWdpbiBhbGxvdy1mb3Jtc1wiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgKSA6IG51bGx9XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgKX1cbiAgICAgIDwvbWFpbj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVN0YXRlIiwidXNlUm91dGVyIiwiQnV0dG9uIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkxvZ091dCIsIlVzZXIiLCJNZW51IiwiWCIsIkFjdGl2aXR5IiwiQ2hldnJvbkxlZnQiLCJDaGV2cm9uUmlnaHQiLCJVc2VycyIsIkxvYWRlcjIiLCJBbGVydENpcmNsZSIsIkhvbWUiLCJCcmFpbiIsIkJhckNoYXJ0MyIsIkxpbmVDaGFydCIsImFnZW50cyIsImlkIiwibmFtZSIsInVybCIsInR5cGUiLCJzdGF0dXMiLCJsYXN0QWN0aXZlIiwidGFza3NDb21wbGV0ZWQiLCJzdWNjZXNzUmF0ZSIsImRhc2hib2FyZFN0YXRzIiwidGl0bGUiLCJ2YWx1ZSIsInN1YnRpdGxlIiwiY29sb3IiLCJ0cmVuZCIsInRyZW5kRGlyZWN0aW9uIiwiYWdlbnRBY3Rpdml0eSIsImFnZW50IiwiYWN0aW9uIiwidGltZSIsImR1cmF0aW9uIiwicGVyZm9ybWFuY2VEYXRhIiwic3lzdGVtTG9hZCIsImNwdSIsIm1lbW9yeSIsIm5ldHdvcmsiLCJhZ2VudFBlcmZvcm1hbmNlIiwiY29tcGxldGVkIiwic3VjY2VzcyIsImF2Z1RpbWUiLCJ0YXNrRGlzdHJpYnV0aW9uIiwiY2F0ZWdvcnkiLCJjb3VudCIsInBlcmNlbnRhZ2UiLCJEYXNoYm9hcmRQYWdlIiwic2VsZWN0ZWRWaWV3Iiwic2V0U2VsZWN0ZWRWaWV3IiwidXNlcm5hbWUiLCJzZXRVc2VybmFtZSIsInNpZGViYXJDb2xsYXBzZWQiLCJzZXRTaWRlYmFyQ29sbGFwc2VkIiwibW9iaWxlTWVudU9wZW4iLCJzZXRNb2JpbGVNZW51T3BlbiIsInRvdWNoU3RhcnRYIiwic2V0VG91Y2hTdGFydFgiLCJ0b3VjaEN1cnJlbnRYIiwic2V0VG91Y2hDdXJyZW50WCIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImlmcmFtZUVycm9yIiwic2V0SWZyYW1lRXJyb3IiLCJyb3V0ZXIiLCJpc0xvZ2dlZEluIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInN0b3JlZFVzZXJuYW1lIiwicHVzaCIsImhhbmRsZUtleURvd24iLCJlIiwia2V5IiwiaGFuZGxlUmVzaXplIiwid2luZG93IiwiaW5uZXJXaWR0aCIsImRvY3VtZW50IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJoYW5kbGVMb2dvdXQiLCJyZW1vdmVJdGVtIiwidG9nZ2xlU2lkZWJhciIsInRvZ2dsZU1vYmlsZU1lbnUiLCJoYW5kbGVUb3VjaFN0YXJ0IiwidG91Y2hlcyIsImNsaWVudFgiLCJoYW5kbGVUb3VjaE1vdmUiLCJoYW5kbGVUb3VjaEVuZCIsImRlbHRhWCIsInRocmVzaG9sZCIsImhhbmRsZUJhY2tkcm9wQ2xpY2siLCJzdG9wUHJvcGFnYXRpb24iLCJoYW5kbGVWaWV3U2VsZWN0IiwidmlldyIsInNldFRpbWVvdXQiLCJzZWxlY3RlZEFnZW50RGF0YSIsImZpbmQiLCJkaXYiLCJjbGFzc05hbWUiLCJvbkNsaWNrIiwib25Ub3VjaFN0YXJ0Iiwib25Ub3VjaE1vdmUiLCJvblRvdWNoRW5kIiwiYXNpZGUiLCJoMSIsInZhcmlhbnQiLCJzaXplIiwicCIsIm5hdiIsImJ1dHRvbiIsInVuZGVmaW5lZCIsInNwYW4iLCJzb21lIiwiaDQiLCJtYXAiLCJoMyIsIm1haW4iLCJzdGF0IiwiaW5kZXgiLCJzbGljZSIsImxvYWQiLCJzdHlsZSIsIndpZHRoIiwiYWN0aXZpdHkiLCJpZnJhbWUiLCJzcmMiLCJvbkVycm9yIiwic2FuZGJveCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});